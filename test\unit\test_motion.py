#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运动控制模块单元测试

测试运动控制器的各项功能，包括参数验证、运动指令等。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

import unittest
from unittest.mock import Mock, patch
from src.robot.motion import MotionController
from src.core.validators import ParameterValidator


class TestMotionController(unittest.TestCase):
    """运动控制器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_conn = Mock()
        self.mock_servo = Mock()
        self.mock_error_handler = Mock()
        
        # 设置开发模式
        self.mock_conn.is_development_mode.return_value = True
        self.mock_conn.is_robot_connected.return_value = True
        
        self.motion = MotionController(
            self.mock_conn, 
            self.mock_servo, 
            self.mock_error_handler
        )
    
    def test_move_linear_valid_params(self):
        """测试有效参数的线性运动"""
        target_pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        velocity = 50.0
        blending_radius = 1.0
        
        result = self.motion.move_linear(target_pos, velocity, blending_radius)
        self.assertTrue(result)
    
    def test_move_linear_invalid_position(self):
        """测试无效位置参数的线性运动"""
        # 位置参数不足6个
        target_pos = [100.0, 200.0, 300.0]
        velocity = 50.0
        blending_radius = 1.0
        
        result = self.motion.move_linear(target_pos, velocity, blending_radius)
        self.assertFalse(result)
    
    def test_move_linear_invalid_velocity(self):
        """测试无效速度参数的线性运动"""
        target_pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        velocity = -10.0  # 负速度
        blending_radius = 1.0
        
        result = self.motion.move_linear(target_pos, velocity, blending_radius)
        self.assertFalse(result)
    
    def test_move_joint_valid_params(self):
        """测试有效参数的关节运动"""
        target_pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        velocity = 50.0
        blending_radius = 1.0
        
        result = self.motion.move_joint(target_pos, velocity, blending_radius)
        self.assertTrue(result)
    
    def test_set_speed_valid(self):
        """测试有效的速度设置"""
        speed_percentage = 75
        
        result = self.motion.set_speed(speed_percentage)
        self.assertTrue(result)
    
    def test_set_speed_invalid(self):
        """测试无效的速度设置"""
        speed_percentage = 150  # 超出范围
        
        result = self.motion.set_speed(speed_percentage)
        self.assertFalse(result)
    
    def test_get_speed(self):
        """测试获取速度"""
        result = self.motion.get_speed()
        self.assertIsInstance(result, int)
        self.assertEqual(result, 50)  # 开发模式返回50
    
    def test_servo_check_before_motion(self):
        """测试运动前的伺服状态检查"""
        # 设置伺服状态检查返回False
        self.mock_servo.check_servo_ready_for_motion.return_value = False
        
        target_pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        velocity = 50.0
        blending_radius = 1.0
        
        # 在非开发模式下测试
        self.mock_conn.is_development_mode.return_value = False
        
        result = self.motion.move_linear(target_pos, velocity, blending_radius)
        self.assertFalse(result)
        
        # 验证伺服检查被调用
        self.mock_servo.check_servo_ready_for_motion.assert_called_once()


class TestParameterValidator(unittest.TestCase):
    """参数验证器测试类"""
    
    def test_validate_position_valid(self):
        """测试有效位置验证"""
        position = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        result = ParameterValidator.validate_position(position)
        self.assertTrue(result)
    
    def test_validate_position_invalid_length(self):
        """测试无效长度的位置验证"""
        position = [100.0, 200.0, 300.0]  # 只有3个元素
        
        with self.assertRaises(ValueError):
            ParameterValidator.validate_position(position)
    
    def test_validate_position_invalid_type(self):
        """测试无效类型的位置验证"""
        position = "not a list"
        
        with self.assertRaises(ValueError):
            ParameterValidator.validate_position(position)
    
    def test_validate_velocity_valid(self):
        """测试有效速度验证"""
        velocity = 50.0
        result = ParameterValidator.validate_velocity(velocity)
        self.assertTrue(result)
    
    def test_validate_velocity_invalid(self):
        """测试无效速度验证"""
        velocity = -10.0  # 负速度
        
        with self.assertRaises(ValueError):
            ParameterValidator.validate_velocity(velocity)
    
    def test_validate_speed_percentage_valid(self):
        """测试有效速度比例验证"""
        speed = 75
        result = ParameterValidator.validate_speed_percentage(speed)
        self.assertTrue(result)
    
    def test_validate_speed_percentage_invalid(self):
        """测试无效速度比例验证"""
        speed = 150  # 超出范围
        
        with self.assertRaises(ValueError):
            ParameterValidator.validate_speed_percentage(speed)


if __name__ == '__main__':
    unittest.main()
