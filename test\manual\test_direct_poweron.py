# test_direct_poweron.py
"""
测试跳过set_servo_state直接上电

验证官方文档的要求是否严格：
- 官方文档说：调用set_servo_poweron之前需要先调用set_servo_state(1)
- 我们测试：如果跳过set_servo_state(1)直接调用set_servo_poweron会怎样？

这个测试可以帮助我们理解：
1. 官方文档的要求是建议还是强制要求
2. 是否存在其他的上电路径
3. 机器人的实际状态管理机制
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_direct_poweron():
    """
    测试跳过set_servo_state直接上电
    """
    socket_fd = None
    
    try:
        print("=" * 80)
        print("跳过set_servo_state直接上电测试")
        print("=" * 80)
        print("测试目的：验证官方文档要求的严格性")
        print("官方文档：调用set_servo_poweron之前需要先调用set_servo_state(1)")
        print("我们测试：跳过set_servo_state(1)直接调用set_servo_poweron")
        print("=" * 80)
        
        # 连接
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败")
            return False
        
        print(f"[成功] 连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        print("\n" + "="*60)
        print("步骤1: 清除错误")
        print("="*60)
        
        result = nrc.clear_error(socket_fd)
        print(f"clear_error() 结果: {result}")
        if result == 0:
            print("✅ 清除错误成功")
        else:
            print(f"❌ 清除错误失败，代码: {result}")
        
        time.sleep(1)
        
        print("\n" + "="*60)
        print("步骤2: 跳过set_servo_state，直接尝试上电")
        print("="*60)
        print("🚫 故意跳过 set_servo_state(1)")
        print("⚡ 直接调用 set_servo_poweron()")
        
        result = nrc.set_servo_poweron(socket_fd)
        print(f"set_servo_poweron() 结果: {result}")
        
        if result == 0:
            print("🎉 惊喜！直接上电成功了！")
            print("这说明：")
            print("  - 官方文档的要求可能不是强制的")
            print("  - 或者机器人已经处于合适的状态")
            print("  - 或者存在内部的自动状态管理")
            direct_poweron_success = True
        else:
            print(f"❌ 直接上电失败，代码: {result}")
            print("这证实了官方文档的要求：")
            print("  - set_servo_state(1) 确实是必要的前置条件")
            print("  - 机器人严格按照状态机管理")
            direct_poweron_success = False
        
        time.sleep(2)
        
        print("\n" + "="*60)
        print("步骤3: 验证当前状态")
        print("="*60)
        
        # 尝试获取当前状态
        status_value = 0
        result = nrc.get_servo_state(socket_fd, status_value)
        print(f"get_servo_state() 结果: {result}")
        
        if isinstance(result, list) and len(result) >= 2:
            result_code = result[0]
            servo_state = result[1]
            
            if result_code == 0:
                state_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
                state_desc = state_names.get(servo_state, f"未知状态({servo_state})")
                print(f"当前伺服状态: {servo_state} ({state_desc})")
                
                if direct_poweron_success:
                    if servo_state == 3:
                        print("✅ 状态一致：直接上电成功且状态为运行状态")
                    else:
                        print(f"⚠️ 状态异常：上电成功但状态不是运行状态")
                else:
                    print(f"ℹ️ 上电失败时的状态：{state_desc}")
            else:
                print(f"❌ 无法获取伺服状态，代码: {result_code}")
        else:
            print("❌ 获取伺服状态返回格式异常")
        
        print("\n" + "="*60)
        print("步骤4: 如果直接上电失败，尝试标准流程")
        print("="*60)
        
        if not direct_poweron_success:
            print("直接上电失败，现在尝试标准流程...")
            
            # 尝试标准流程
            print("4.1: 尝试 set_servo_state(1)")
            result = nrc.set_servo_state(socket_fd, 1)
            print(f"set_servo_state(1) 结果: {result}")
            
            if result == 0:
                print("✅ 设置伺服就绪状态成功")
                time.sleep(1)
                
                print("4.2: 再次尝试上电")
                result = nrc.set_servo_poweron(socket_fd)
                print(f"set_servo_poweron() 结果: {result}")
                
                if result == 0:
                    print("✅ 标准流程上电成功！")
                    print("结论：必须按照官方文档的顺序执行")
                else:
                    print(f"❌ 即使按标准流程也失败，代码: {result}")
                    print("问题可能在于机器人的硬件状态")
            else:
                print(f"❌ 设置伺服状态失败，代码: {result}")
                print("问题的根源在于无法设置伺服状态")
        
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        
        if direct_poweron_success:
            print("🎉 重要发现：可以跳过set_servo_state直接上电！")
            print("这意味着：")
            print("  - 官方文档的要求可能是最佳实践而非强制要求")
            print("  - 机器人可能有内部的状态管理逻辑")
            print("  - 在某些条件下可以简化上电流程")
        else:
            print("📋 验证了官方文档的要求：")
            print("  - set_servo_state(1) 确实是set_servo_poweron的前置条件")
            print("  - 机器人严格按照状态机进行管理")
            print("  - 必须按照官方文档的顺序执行")
        
        return direct_poweron_success
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                print("\n正在安全关闭...")
                nrc.set_servo_poweroff(socket_fd)
                time.sleep(1)
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except:
                pass


def test_multiple_poweron_attempts():
    """
    测试多次上电尝试
    """
    socket_fd = None
    
    try:
        print("=" * 80)
        print("多次上电尝试测试")
        print("=" * 80)
        
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            return False
        
        print(f"连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        # 清除错误
        nrc.clear_error(socket_fd)
        time.sleep(1)
        
        # 多次尝试直接上电
        for i in range(3):
            print(f"\n第{i+1}次尝试直接上电...")
            result = nrc.set_servo_poweron(socket_fd)
            print(f"set_servo_poweron() 结果: {result}")
            
            if result == 0:
                print(f"✅ 第{i+1}次尝试成功！")
                return True
            else:
                print(f"❌ 第{i+1}次尝试失败")
                time.sleep(2)
        
        print("\n所有直接上电尝试都失败了")
        return False
        
    except Exception as e:
        print(f"测试中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                nrc.disconnect_robot(socket_fd)
            except:
                pass


def main():
    """
    主函数
    """
    print("INEXBOT 跳过set_servo_state直接上电测试")
    print()
    print("这个测试验证是否可以跳过官方文档要求的前置步骤")
    print()
    
    print("测试选项:")
    print("1. 跳过set_servo_state直接上电测试")
    print("2. 多次上电尝试测试")
    print("3. 两个测试都运行")
    
    choice = input("\n请选择测试 (1/2/3): ").strip()
    
    if choice == "1":
        test_direct_poweron()
    elif choice == "2":
        test_multiple_poweron_attempts()
    elif choice == "3":
        result1 = test_direct_poweron()
        print("\n" + "="*80)
        result2 = test_multiple_poweron_attempts()
        
        print("\n" + "="*80)
        print("综合测试结果:")
        print(f"直接上电测试: {'成功' if result1 else '失败'}")
        print(f"多次尝试测试: {'成功' if result2 else '失败'}")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
