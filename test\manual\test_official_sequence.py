# test_official_sequence.py
"""
测试官方文档要求的精确上电流程

根据官方文档，set_servo_poweron()的要求：
1. 调用该函数之前需要先调用set_servo_state(SOCKETFD socketFd,1)将伺服设置为1（就绪状态）
2. 机器人上电成功后调用 get_servo_state(SOCKETFD socketFd)为3伺服运行状态
3. 该函数只有伺服状态为1（就绪状态）时调用生效

测试重点：
- 验证set_servo_state(1)是否是set_servo_poweron()的必要前提
- 分析为什么set_servo_state()调用失败
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_official_poweron_sequence():
    """
    测试官方文档要求的精确上电流程
    """
    socket_fd = None
    
    try:
        print("=" * 80)
        print("官方文档要求的精确上电流程测试")
        print("=" * 80)
        
        # 连接
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败")
            return False
        
        print(f"[成功] 连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        print("\n" + "="*60)
        print("步骤1: 清除错误 (clear_error)")
        print("="*60)
        
        result = nrc.clear_error(socket_fd)
        print(f"clear_error() 结果: {result}")
        if result == 0:
            print("✅ 清除错误成功")
        else:
            print(f"❌ 清除错误失败，代码: {result}")
            return False
        
        time.sleep(1)
        
        print("\n" + "="*60)
        print("步骤2: 设置伺服为就绪状态 (set_servo_state(1))")
        print("官方文档要求：调用set_servo_poweron之前必须先调用此函数")
        print("="*60)
        
        result = nrc.set_servo_state(socket_fd, 1)
        print(f"set_servo_state(1) 结果: {result}")
        if result == 0:
            print("✅ 设置伺服就绪状态成功")
            servo_ready = True
        else:
            print(f"❌ 设置伺服就绪状态失败，代码: {result}")
            print("分析可能的原因：")
            print("  - 机器人可能处于报警状态(2)或运行状态(3)")
            print("  - 安全回路可能未连接")
            print("  - 急停按钮可能被按下")
            print("  - 需要特定的硬件条件")
            servo_ready = False
        
        time.sleep(2)
        
        print("\n" + "="*60)
        print("步骤3: 尝试上电 (set_servo_poweron)")
        print("官方文档要求：只有伺服状态为1（就绪状态）时调用生效")
        print("="*60)
        
        if servo_ready:
            print("伺服已就绪，尝试上电...")
        else:
            print("伺服未就绪，但仍尝试上电以验证官方文档的要求...")
        
        result = nrc.set_servo_poweron(socket_fd)
        print(f"set_servo_poweron() 结果: {result}")
        if result == 0:
            print("✅ 上电成功")
            poweron_success = True
        else:
            print(f"❌ 上电失败，代码: {result}")
            if not servo_ready:
                print("这证实了官方文档的要求：必须先设置伺服为就绪状态")
            poweron_success = False
        
        time.sleep(2)
        
        print("\n" + "="*60)
        print("步骤4: 验证最终状态")
        print("官方文档要求：上电成功后应为运行状态(3)")
        print("="*60)
        
        status_value = 0
        result = nrc.get_servo_state(socket_fd, status_value)
        print(f"get_servo_state() 结果: {result}")
        
        if isinstance(result, list) and len(result) >= 2:
            result_code = result[0]
            servo_state = result[1]
            
            if result_code == 0:
                state_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
                state_desc = state_names.get(servo_state, f"未知状态({servo_state})")
                print(f"当前伺服状态: {servo_state} ({state_desc})")
                
                if poweron_success and servo_state == 3:
                    print("✅ 状态验证成功：上电后确实为运行状态")
                elif poweron_success and servo_state != 3:
                    print(f"⚠️ 状态异常：上电成功但状态不是运行状态")
                else:
                    print("ℹ️ 由于上电失败，状态验证跳过")
            else:
                print(f"❌ 无法获取伺服状态，代码: {result_code}")
        else:
            print("❌ 获取伺服状态返回格式异常")
        
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        
        print(f"1. 清除错误: {'成功' if result == 0 else '失败'}")
        print(f"2. 设置伺服就绪: {'成功' if servo_ready else '失败'}")
        print(f"3. 机器人上电: {'成功' if poweron_success else '失败'}")
        
        if not servo_ready:
            print("\n关键发现：")
            print("- set_servo_state(1) 失败是上电失败的根本原因")
            print("- 这证实了官方文档的要求：必须先设置伺服为就绪状态")
            print("- 问题可能在于机器人的物理状态或安全条件")
        
        return servo_ready and poweron_success
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                # 尝试下电
                print("\n正在安全关闭...")
                nrc.set_servo_poweroff(socket_fd)
                time.sleep(1)
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except:
                pass


def test_servo_state_requirements():
    """
    测试set_servo_state的调用要求
    """
    socket_fd = None
    
    try:
        print("=" * 80)
        print("测试set_servo_state的调用要求")
        print("官方文档：只有伺服状态为0（停止状态）或1（就绪状态）时调用生效")
        print("伺服状态为2（报警状态）或3（运行状态）时不能直接设置伺服状态")
        print("=" * 80)
        
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            return False
        
        print(f"连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        # 清除错误
        print("1. 清除错误...")
        nrc.clear_error(socket_fd)
        time.sleep(1)
        
        # 尝试获取当前状态
        print("2. 尝试获取当前伺服状态...")
        status_value = 0
        result = nrc.get_servo_state(socket_fd, status_value)
        print(f"get_servo_state 结果: {result}")
        
        if isinstance(result, list) and len(result) >= 2:
            result_code = result[0]
            current_state = result[1]
            
            if result_code == 0:
                state_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
                print(f"当前状态: {current_state} ({state_names.get(current_state, '未知')})")
                
                # 根据当前状态分析set_servo_state的可行性
                if current_state in [0, 1]:
                    print(f"✅ 当前状态({current_state})允许调用set_servo_state")
                elif current_state in [2, 3]:
                    print(f"❌ 当前状态({current_state})不允许调用set_servo_state")
                    print("这解释了为什么set_servo_state(1)失败")
            else:
                print(f"无法获取状态，错误代码: {result_code}")
        else:
            print("获取状态失败，这本身就说明了问题")
        
        # 无论如何都尝试调用set_servo_state
        print("3. 尝试调用set_servo_state(1)...")
        result = nrc.set_servo_state(socket_fd, 1)
        print(f"set_servo_state(1) 结果: {result}")
        
        if result == 0:
            print("✅ 设置成功")
        else:
            print(f"❌ 设置失败，代码: {result}")
            print("这证实了官方文档的限制条件")
        
        return True
        
    except Exception as e:
        print(f"测试中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                nrc.disconnect_robot(socket_fd)
            except:
                pass


def main():
    """
    主函数
    """
    print("INEXBOT 官方上电流程精确测试")
    print()
    print("这个测试验证官方文档中set_servo_poweron的要求")
    print()
    
    print("测试选项:")
    print("1. 官方上电流程测试")
    print("2. 伺服状态要求测试")
    print("3. 两个测试都运行")
    
    choice = input("\n请选择测试 (1/2/3): ").strip()
    
    if choice == "1":
        test_official_poweron_sequence()
    elif choice == "2":
        test_servo_state_requirements()
    elif choice == "3":
        test_official_poweron_sequence()
        print("\n" + "="*80)
        test_servo_state_requirements()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
