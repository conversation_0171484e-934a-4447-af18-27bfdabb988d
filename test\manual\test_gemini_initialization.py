# test_gemini_initialization.py
"""
测试基于Gemini分析的完整机器人初始化流程

这个测试验证我们改进后的enable_servos()方法是否正确实现了
Gemini建议的完整状态检查和处理逻辑。

测试流程：
1. 清除历史错误
2. 检查当前伺服状态
3. 根据状态执行相应处理
4. 确保进入就绪状态
5. 调用set_servo_state(1)确认
6. 执行上电操作
7. 验证最终状态
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.robot.interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT


def test_gemini_initialization():
    """
    测试基于Gemini分析的完整初始化流程
    """
    robot = None
    
    try:
        print("=" * 80)
        print("基于Gemini分析的完整机器人初始化流程测试")
        print("=" * 80)
        print()
        
        # 创建机器人接口（自动连接）
        print(f"创建机器人接口并连接 {ROBOT_IP}:{ROBOT_PORT}...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        # 检查连接状态
        if not robot.is_robot_connected():
            print("[错误] 机器人连接失败")
            return False

        print("[成功] 机器人连接成功")
        print()
        
        # 显示连接前的状态
        print("连接后初始状态检查:")
        print("-" * 40)
        
        # 检查初始伺服状态
        initial_state = robot.get_servo_state()
        if initial_state:
            print(f"初始伺服状态: {initial_state['description']} (代码: {initial_state['status_code']})")
        else:
            print("无法获取初始伺服状态")
        
        # 检查机器人运行状态
        running_state = robot.get_robot_running_state()
        if running_state:
            print(f"初始运行状态: {running_state['description']} (代码: {running_state['status_code']})")
        else:
            print("无法获取初始运行状态")
        
        print()
        print("=" * 80)
        print("开始执行完整初始化流程...")
        print("=" * 80)
        
        # 执行改进后的伺服使能流程
        success = robot.enable_servos()
        
        print()
        print("=" * 80)
        print("初始化流程完成，检查最终状态...")
        print("=" * 80)
        
        # 检查最终状态
        final_servo_state = robot.get_servo_state()
        if final_servo_state:
            print(f"最终伺服状态: {final_servo_state['description']} (代码: {final_servo_state['status_code']})")
        
        final_running_state = robot.get_robot_running_state()
        if final_running_state:
            print(f"最终运行状态: {final_running_state['description']} (代码: {final_running_state['status_code']})")
        
        # 尝试获取当前位置以验证机器人功能
        print()
        print("验证机器人功能:")
        print("-" * 40)
        
        current_pos = robot.get_current_position()
        if current_pos:
            print(f"当前位置: {current_pos}")
        else:
            print("无法获取当前位置")
        
        # 检查机器人类型
        robot_type = robot.get_robot_type()
        if robot_type:
            print(f"机器人类型: {robot_type}")
        
        print()
        if success:
            print("[成功] 完整初始化流程测试成功！")
            print("机器人已准备好接受运动指令")
        else:
            print("[失败] 完整初始化流程测试失败！")
            print("请检查机器人状态和连接")
        
        return success
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if robot is not None:
            print()
            print("=" * 80)
            print("正在安全关闭...")
            print("=" * 80)
            
            try:
                # 安全关闭伺服
                print("关闭伺服...")
                robot.disable_servos()
                time.sleep(1)
                
                # 断开连接
                print("断开连接...")
                robot.disconnect()
                print("[成功] 安全关闭完成")
            except Exception as e:
                print(f"[警告] 关闭过程中发生异常: {e}")


def test_state_transitions():
    """
    测试各种状态转换场景
    """
    robot = None
    
    try:
        print("=" * 80)
        print("状态转换场景测试")
        print("=" * 80)
        
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        if not robot.is_robot_connected():
            print("[错误] 机器人连接失败")
            return False

        print("[成功] 机器人连接成功")
        
        # 测试场景1：从任意状态到运行状态
        print("\n场景1: 从当前状态到运行状态")
        print("-" * 50)
        
        initial_state = robot.get_servo_state()
        if initial_state:
            print(f"起始状态: {initial_state['description']} (代码: {initial_state['status_code']})")
        
        success1 = robot.enable_servos()
        print(f"场景1结果: {'成功' if success1 else '失败'}")
        
        if success1:
            # 测试场景2：从运行状态再次调用enable_servos
            print("\n场景2: 从运行状态再次调用enable_servos")
            print("-" * 50)
            
            success2 = robot.enable_servos()
            print(f"场景2结果: {'成功' if success2 else '失败'}")
            
            # 测试场景3：下电后再上电
            print("\n场景3: 下电后再上电")
            print("-" * 50)
            
            print("执行下电...")
            robot.disable_servos()
            time.sleep(2)
            
            print("再次上电...")
            success3 = robot.enable_servos()
            print(f"场景3结果: {'成功' if success3 else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"状态转换测试中发生错误: {e}")
        return False
        
    finally:
        if robot is not None:
            try:
                robot.disable_servos()
                robot.disconnect()
            except:
                pass


def main():
    """
    主函数
    """
    print("INEXBOT 基于Gemini分析的完整初始化流程测试")
    print()
    print("这个测试验证我们改进后的enable_servos()方法")
    print("是否正确实现了完整的状态检查和处理逻辑")
    print()
    
    print("测试选项:")
    print("1. 完整初始化流程测试")
    print("2. 状态转换场景测试")
    print("3. 两个测试都运行")
    
    choice = input("\n请选择测试 (1/2/3): ").strip()
    
    if choice == "1":
        test_gemini_initialization()
    elif choice == "2":
        test_state_transitions()
    elif choice == "3":
        print("\n运行完整初始化流程测试...")
        result1 = test_gemini_initialization()
        
        print("\n" + "="*80)
        print("运行状态转换场景测试...")
        result2 = test_state_transitions()
        
        print("\n" + "="*80)
        print("测试总结:")
        print(f"完整初始化流程: {'成功' if result1 else '失败'}")
        print(f"状态转换场景: {'成功' if result2 else '失败'}")
    else:
        print("无效选择，退出测试")


if __name__ == "__main__":
    main()
