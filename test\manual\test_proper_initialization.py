# test_proper_initialization.py
"""
机器人正确初始化测试脚本

按照INEXBOT官方文档的要求，正确初始化机械臂：
1. 连接机器人
2. 清除错误
3. 检查并设置机器人模式为远程模式
4. 设置伺服为就绪状态
5. 机器人上电
6. 测试基础功能
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface进行低级别测试
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_proper_initialization():
    """
    按照正确顺序初始化机械臂
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("机器人正确初始化测试")
        print("=" * 60)
        
        # 步骤1: 连接机器人
        print("步骤1: 连接机器人")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败，错误代码: {socket_fd}")
            return False
        
        print(f"[成功] 连接成功，套接字FD: {socket_fd}")
        print("-" * 40)
        
        # 步骤2: 清除错误
        print("步骤2: 清除错误")
        result = nrc.clear_error(socket_fd)
        print(f"清除错误结果: {result}")
        if result == 0:
            print("[成功] 错误清除成功")
        else:
            print(f"[警告] 清除错误失败，代码: {result}")
        
        time.sleep(1)  # 等待错误清除生效
        print("-" * 40)
        
        # 步骤3: 检查当前模式
        print("步骤3: 检查和设置机器人模式")
        
        # 获取当前模式
        mode_value = 0
        result = nrc.get_current_mode(socket_fd, mode_value)
        print(f"获取当前模式结果: {result}")
        
        if isinstance(result, list) and len(result) >= 2:
            result_code = result[0]
            current_mode = result[1]
            if result_code == 0:
                print(f"当前模式: {current_mode}")
                # 模式说明：0=示教模式, 1=运行模式, 2=远程模式
                mode_names = {0: "示教模式", 1: "运行模式", 2: "远程模式"}
                print(f"模式说明: {mode_names.get(current_mode, '未知模式')}")
                
                # 如果不是远程模式，设置为远程模式
                if current_mode != 2:
                    print("设置为远程模式...")
                    result = nrc.set_current_mode(socket_fd, 2)
                    print(f"设置模式结果: {result}")
                    if result == 0:
                        print("[成功] 已设置为远程模式")
                        time.sleep(1)
                    else:
                        print(f"[警告] 设置远程模式失败，代码: {result}")
                else:
                    print("[成功] 已经是远程模式")
            else:
                print(f"[警告] 获取模式失败，代码: {result_code}")
        else:
            print(f"[警告] 获取模式返回格式异常: {result}")
        
        print("-" * 40)
        
        # 步骤4: 检查伺服状态
        print("步骤4: 检查伺服状态")
        
        status_value = 0
        result = nrc.get_servo_state(socket_fd, status_value)
        print(f"获取伺服状态结果: {result}")
        
        if isinstance(result, list) and len(result) >= 2:
            result_code = result[0]
            servo_state = result[1]
            if result_code == 0:
                # 伺服状态：0=停止, 1=就绪, 2=报警, 3=运行
                state_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
                print(f"当前伺服状态: {servo_state} ({state_names.get(servo_state, '未知状态')})")
                
                # 根据当前状态决定下一步操作
                if servo_state == 0:  # 停止状态，可以设置为就绪
                    print("伺服处于停止状态，设置为就绪状态...")
                    result = nrc.set_servo_state(socket_fd, 1)
                    print(f"设置伺服状态结果: {result}")
                    if result == 0:
                        print("[成功] 伺服设置为就绪状态")
                        time.sleep(2)  # 等待状态更新
                    else:
                        print(f"[错误] 设置伺服状态失败，代码: {result}")
                        return False
                        
                elif servo_state == 1:  # 已经是就绪状态
                    print("[成功] 伺服已经是就绪状态")
                    
                elif servo_state == 2:  # 报警状态
                    print("[错误] 伺服处于报警状态，需要先处理报警")
                    return False
                    
                elif servo_state == 3:  # 运行状态
                    print("[成功] 伺服已经是运行状态")
                    
            else:
                print(f"[错误] 获取伺服状态失败，代码: {result_code}")
                return False
        else:
            print(f"[错误] 获取伺服状态返回格式异常: {result}")
            return False
        
        print("-" * 40)
        
        # 步骤5: 机器人上电（如果伺服是就绪状态）
        print("步骤5: 机器人上电")
        
        # 再次检查伺服状态
        status_value = 0
        result = nrc.get_servo_state(socket_fd, status_value)
        if isinstance(result, list) and len(result) >= 2 and result[0] == 0:
            servo_state = result[1]
            if servo_state == 1:  # 就绪状态，可以上电
                print("伺服处于就绪状态，执行上电...")
                result = nrc.set_servo_poweron(socket_fd)
                print(f"上电结果: {result}")
                if result == 0:
                    print("[成功] 机器人上电成功")
                    time.sleep(2)  # 等待上电完成
                    
                    # 检查上电后的状态
                    status_value = 0
                    result = nrc.get_servo_state(socket_fd, status_value)
                    if isinstance(result, list) and len(result) >= 2 and result[0] == 0:
                        servo_state = result[1]
                        state_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
                        print(f"上电后伺服状态: {servo_state} ({state_names.get(servo_state, '未知状态')})")
                        if servo_state == 3:
                            print("[成功] 机器人已进入运行状态")
                        else:
                            print(f"[警告] 上电后状态异常: {servo_state}")
                else:
                    print(f"[错误] 机器人上电失败，代码: {result}")
            elif servo_state == 3:  # 已经是运行状态
                print("[成功] 机器人已经是运行状态")
            else:
                print(f"[警告] 伺服状态不适合上电: {servo_state}")
        
        print("-" * 40)
        
        # 步骤6: 测试基础功能
        print("步骤6: 测试基础功能")
        
        # 测试获取运行状态
        print("测试获取运行状态...")
        status_value = 0
        result = nrc.get_robot_running_state(socket_fd, status_value)
        print(f"运行状态结果: {result}")
        if isinstance(result, list) and len(result) >= 2 and result[0] == 0:
            running_state = result[1]
            state_names = {0: "停止状态", 1: "运行状态", 2: "暂停状态", 3: "错误状态", 4: "急停状态", 5: "准备状态"}
            print(f"[成功] 运行状态: {running_state} ({state_names.get(running_state, '未知状态')})")
        else:
            print(f"[警告] 获取运行状态失败: {result}")
        
        # 测试获取位置
        print("测试获取位置...")
        try:
            position = [0.0] * 6
            result = nrc.get_current_position(socket_fd, 1, position)
            print(f"获取位置结果: {result}")
            if result == 0:
                print(f"[成功] 当前位置: {position}")
            else:
                print(f"[警告] 获取位置失败，代码: {result}")
        except Exception as e:
            print(f"获取位置异常: {e}")
        
        # 测试速度控制
        print("测试速度控制...")
        speed_value = 0
        result = nrc.get_speed(socket_fd, speed_value)
        print(f"获取速度结果: {result}")
        if isinstance(result, list) and len(result) >= 2 and result[0] == 0:
            current_speed = result[1]
            print(f"[成功] 当前速度: {current_speed}%")
        else:
            print(f"[警告] 获取速度失败: {result}")
        
        print("-" * 40)
        print("[成功] 机器人初始化完成！")
        return True
        
    except Exception as e:
        print(f"初始化过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            print("-" * 40)
            print("正在安全关闭...")
            
            try:
                # 检查当前状态，如果是运行状态则下电
                status_value = 0
                result = nrc.get_servo_state(socket_fd, status_value)
                if isinstance(result, list) and len(result) >= 2 and result[0] == 0:
                    servo_state = result[1]
                    if servo_state == 3:  # 运行状态
                        print("机器人下电...")
                        nrc.set_servo_poweroff(socket_fd)
                        time.sleep(1)
                
                # 设置伺服为停止状态
                print("设置伺服为停止状态...")
                nrc.set_servo_state(socket_fd, 0)
                time.sleep(1)
                
                # 断开连接
                print("断开连接...")
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except Exception as e:
                print(f"[警告] 关闭过程中发生异常: {e}")


def main():
    """
    主函数
    """
    print("机器人正确初始化测试")
    print("该测试将按照INEXBOT官方文档要求的正确顺序初始化机械臂")
    print()
    
    user_input = input("确认开始测试，输入 'yes' 继续: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return
    
    if test_proper_initialization():
        print("\n[成功] 机器人初始化测试完成！")
    else:
        print("\n[失败] 机器人初始化测试失败！")


if __name__ == "__main__":
    main()
