#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的机器人控制演示脚本

展示新的模块化架构和向后兼容性。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 演示向后兼容性 - 使用原有的导入方式
from hardware.robot_interface import RobotInterface

# 导入配置
try:
    from config import ROBOT_IP, ROBOT_PORT
except ImportError:
    # 如果config模块导入失败，使用默认值
    ROBOT_IP = "************"
    ROBOT_PORT = 6001
    print("[警告] 使用默认配置: IP=************, PORT=6001")


def demo_basic_functionality():
    """演示基本功能"""
    print("="*60)
    print("演示1: 基本功能测试")
    print("="*60)
    print("使用标准导入方式：from hardware.robot_interface import RobotInterface")

    try:
        # 创建机器人接口
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        print("[成功] 接口创建测试通过")
        print(f"[成功] 机器人接口创建成功")
        print(f"[成功] 开发模式: {robot.is_development_mode()}")

        # 测试基本功能
        position = robot.get_current_position()
        print(f"[成功] 获取位置: {position}")

        # 测试伺服控制
        if robot.enable_servos():
            print("[成功] 伺服使能成功")
            robot.disable_servos()
            print("[成功] 伺服关闭成功")

        robot.disconnect()
        return True

    except Exception as e:
        print(f"[错误] 基本功能测试失败: {e}")
        return False


def demo_new_architecture():
    """演示新架构的功能"""
    print("\n" + "="*60)
    print("演示2: 新架构功能测试")
    print("="*60)
    
    try:
        # 直接使用新的模块化接口
        from src.robot.interface import RobotInterface as NewRobotInterface
        
        robot = NewRobotInterface(ROBOT_IP, ROBOT_PORT)
        
        print("[成功] 新架构接口创建成功")
        
        # 演示模块信息
        module_info = robot.get_module_info()
        print("[成功] 模块信息:")
        for category, info in module_info.items():
            print(f"  {category}: {info}")
        
        # 演示快速启动
        print("\n演示快速启动功能:")
        if robot.quick_start():
            print("[成功] 快速启动成功")
            
            # 演示状态检查
            robot.print_status_summary()
            
            # 演示安全关闭
            if robot.safe_shutdown():
                print("[成功] 安全关闭成功")
        
        return True
        
    except Exception as e:
        print(f"[错误] 新架构功能测试失败: {e}")
        return False


def demo_development_mode():
    """演示开发模式功能"""
    print("\n" + "="*60)
    print("演示3: 开发模式功能")
    print("="*60)
    
    try:
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        
        print(f"开发模式状态: {robot.is_development_mode()}")
        
        # 在开发模式下测试各种功能
        print("\n测试开发模式下的功能:")
        
        # 测试运动控制
        target_pos = [100, 200, 300, 0, 0, 0]
        if robot.move_linear(target_pos, 50, 1):
            print("[成功] 开发模式线性运动模拟成功")
        
        if robot.robot_movej(target_pos, 50, 1):
            print("[成功] 开发模式关节运动模拟成功")
        
        # 测试速度控制
        if robot.set_speed(75):
            print("[成功] 开发模式速度设置模拟成功")
        
        current_speed = robot.get_speed()
        print(f"[成功] 开发模式当前速度: {current_speed}%")
        
        # 测试状态查询
        servo_state = robot.get_servo_state()
        print(f"[成功] 开发模式伺服状态: {servo_state}")
        
        running_state = robot.get_robot_running_state()
        print(f"[成功] 开发模式运行状态: {running_state}")
        
        robot.disconnect()
        return True
        
    except Exception as e:
        print(f"[错误] 开发模式功能测试失败: {e}")
        return False


def demo_error_handling():
    """演示错误处理功能"""
    print("\n" + "="*60)
    print("演示4: 错误处理功能")
    print("="*60)
    
    try:
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        
        # 测试参数验证
        print("测试参数验证:")
        
        # 无效位置参数
        invalid_pos = [100, 200]  # 只有2个坐标
        if not robot.move_linear(invalid_pos, 50, 1):
            print("[成功] 无效位置参数被正确拒绝")
        
        # 无效速度参数
        valid_pos = [100, 200, 300, 0, 0, 0]
        if not robot.move_linear(valid_pos, -10, 1):  # 负速度
            print("[成功] 无效速度参数被正确拒绝")
        
        # 无效速度比例
        if not robot.set_speed(150):  # 超出范围
            print("[成功] 无效速度比例被正确拒绝")
        
        # 测试系统健康检查
        health = robot.check_system_health()
        print(f"[成功] 系统健康检查: {health['overall_health']}")
        
        robot.disconnect()
        return True
        
    except Exception as e:
        print(f"[错误] 错误处理功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("重构后的机器人控制系统演示")
    print("="*60)
    
    demos = [
        ("基本功能", demo_basic_functionality),
        ("模块化架构", demo_new_architecture),
        ("开发模式", demo_development_mode),
        ("错误处理", demo_error_handling),
    ]
    
    results = []
    for name, demo_func in demos:
        try:
            result = demo_func()
            results.append((name, result))
        except Exception as e:
            print(f"[错误] 演示 '{name}' 执行异常: {e}")
            results.append((name, False))
    
    # 打印总结
    print("\n" + "="*60)
    print("演示总结")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "[成功] 通过" if result else "[错误] 失败"
        print(f"{name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个演示通过")
    
    if passed == total:
        print("\n[庆祝] 所有演示通过！重构成功！")
        print("\n重构收益:")
        print("[成功] 代码模块化，易于维护")
        print("[成功] 开发环境友好，无硬件也能运行")
        print("[成功] 增强的错误处理和状态检查")
        print("[成功] 完全向后兼容")
    else:
        print(f"\n[警告] {total - passed} 个演示失败，需要检查相关功能")


if __name__ == "__main__":
    main()
