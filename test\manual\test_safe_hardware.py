#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全硬件测试脚本

该脚本使用安全配置进行硬件测试，确保所有操作都在安全范围内。
适用于首次硬件连接测试和日常安全验证。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from hardware.robot_interface import RobotInterface
from hardware.extruder_interface import ExtruderInterface
from src.core.safety_validator import safety_validator
from config import *


class SafeHardwareTester:
    """安全硬件测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.robot = None
        self.extruder = None
        self.test_results = {}
    
    def print_safety_banner(self):
        """打印安全横幅"""
        print("\n" + "🛡️" * 20)
        print("安全硬件测试")
        print("🛡️" * 20)
        print("⚠️  重要安全提醒：")
        print("   1. 确保机器人工作空间内无人员和障碍物")
        print("   2. 紧急停止按钮随手可及")
        print("   3. 挤出机周围通风良好")
        print("   4. 准备好安全防护设备")
        print("   5. 如有异常立即按紧急停止")
        print("🛡️" * 20)
        
        # 显示安全配置
        safety_validator.print_safety_summary()
        
        # 获取安全测试参数
        safe_params = safety_validator.get_safe_test_parameters()
        print("\n安全测试参数:")
        print(f"  - 移动距离: {safe_params['movement_distance']} mm")
        print(f"  - 运动速度: {safe_params['velocity']} mm/s")
        print(f"  - 测试温度: {safe_params['temperature']}°C")
        print(f"  - 挤出量: {safe_params['extrude_amount']} mm")
        print("🛡️" * 20)
    
    def confirm_safety(self):
        """确认安全条件"""
        print("\n请确认以下安全条件已满足:")
        print("1. 机器人工作空间已清空")
        print("2. 紧急停止按钮功能正常")
        print("3. 挤出机周围安全")
        print("4. 人员已远离设备")
        print("5. 已阅读并理解安全配置")
        
        while True:
            response = input("\n确认所有安全条件已满足? (yes/no): ").lower().strip()
            if response == 'yes':
                print("✅ 安全确认完成，开始测试")
                break
            elif response == 'no':
                print("❌ 请先满足安全条件再进行测试")
                return False
            else:
                print("请输入 'yes' 或 'no'")
        
        return True
    
    def test_robot_connection(self):
        """测试机器人连接"""
        print("\n" + "="*50)
        print("阶段1: 机器人连接测试")
        print("="*50)
        
        try:
            print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
            self.robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
            
            if self.robot.is_robot_connected():
                print("✅ 机器人连接成功")
                
                # 获取基本状态
                print("\n获取机器人状态:")
                running_state = self.robot.get_robot_running_state()
                if running_state:
                    print(f"  - 运行状态: {running_state['description']}")
                
                servo_state = self.robot.get_servo_state()
                if servo_state:
                    print(f"  - 伺服状态: {servo_state['description']}")
                
                current_pos = self.robot.get_current_position()
                if current_pos:
                    print(f"  - 当前位置: {current_pos}")
                
                self.test_results['robot_connection'] = True
                return True
            else:
                print("❌ 机器人连接失败")
                self.test_results['robot_connection'] = False
                return False
                
        except Exception as e:
            print(f"❌ 机器人连接异常: {e}")
            self.test_results['robot_connection'] = False
            return False
    
    def test_safe_robot_movement(self):
        """测试安全机器人运动"""
        print("\n" + "="*50)
        print("阶段2: 安全运动测试")
        print("="*50)
        
        if not self.robot or not self.robot.is_robot_connected():
            print("❌ 机器人未连接，跳过运动测试")
            return False
        
        try:
            # 获取安全测试参数
            safe_params = safety_validator.get_safe_test_parameters()
            
            # 使能伺服
            print("正在使能伺服...")
            if not self.robot.enable_servos():
                print("❌ 伺服使能失败")
                return False
            print("✅ 伺服使能成功")
            
            # 获取当前位置
            current_pos = self.robot.get_current_position()
            if not current_pos:
                print("❌ 无法获取当前位置")
                return False
            
            print(f"当前位置: {current_pos}")
            
            # 计算安全的目标位置（小幅度移动）
            safe_distance = safe_params['movement_distance']
            target_pos = current_pos.copy()
            target_pos[0] += safe_distance  # X轴移动
            
            print(f"目标位置: {target_pos}")
            print(f"移动距离: {safe_distance} mm")
            print(f"运动速度: {safe_params['velocity']} mm/s")
            
            # 执行安全运动
            print("执行安全线性运动...")
            if self.robot.move_linear(
                target_pos, 
                safe_params['velocity'], 
                safe_params['blending_radius']
            ):
                print("✅ 线性运动成功")
                
                # 返回原位置
                print("返回原位置...")
                if self.robot.move_linear(
                    current_pos, 
                    safe_params['velocity'], 
                    safe_params['blending_radius']
                ):
                    print("✅ 返回原位置成功")
                else:
                    print("⚠️ 返回原位置失败")
            else:
                print("❌ 线性运动失败")
                return False
            
            # 关闭伺服
            print("正在关闭伺服...")
            self.robot.disable_servos()
            print("✅ 伺服关闭成功")
            
            self.test_results['robot_movement'] = True
            return True
            
        except Exception as e:
            print(f"❌ 运动测试异常: {e}")
            # 确保伺服关闭
            if self.robot:
                self.robot.disable_servos()
            self.test_results['robot_movement'] = False
            return False
    
    def test_extruder_connection(self):
        """测试挤出机连接"""
        print("\n" + "="*50)
        print("阶段3: 挤出机连接测试")
        print("="*50)
        
        try:
            print(f"正在连接挤出机 {KLIPPER_IP}...")
            self.extruder = ExtruderInterface(KLIPPER_IP)
            
            if self.extruder.get_status():
                print("✅ 挤出机连接成功")
                
                # 获取温度信息
                temps = self.extruder.get_temperatures()
                if temps:
                    print("当前温度信息:")
                    if 'extruder' in temps:
                        ext_temp = temps['extruder']
                        print(f"  - 挤出头: {ext_temp['actual']:.1f}°C (目标: {ext_temp['target']:.1f}°C)")
                    if 'heater_bed' in temps:
                        bed_temp = temps['heater_bed']
                        print(f"  - 热床: {bed_temp['actual']:.1f}°C (目标: {bed_temp['target']:.1f}°C)")
                
                self.test_results['extruder_connection'] = True
                return True
            else:
                print("❌ 挤出机连接失败")
                self.test_results['extruder_connection'] = False
                return False
                
        except Exception as e:
            print(f"❌ 挤出机连接异常: {e}")
            self.test_results['extruder_connection'] = False
            return False
    
    def test_safe_temperature_control(self):
        """测试安全温度控制"""
        print("\n" + "="*50)
        print("阶段4: 安全温度控制测试")
        print("="*50)
        
        if not self.extruder or not self.extruder.get_status():
            print("❌ 挤出机未连接，跳过温度测试")
            return False
        
        try:
            # 获取安全测试参数
            safe_params = safety_validator.get_safe_test_parameters()
            test_temp = safe_params['temperature']
            
            print(f"设置安全测试温度: {test_temp}°C")
            
            # 设置温度（会自动应用安全限制）
            if self.extruder.set_heater_temperature(test_temp):
                print("✅ 温度设置成功")
                
                # 等待一小段时间观察温度变化
                print("等待5秒观察温度变化...")
                time.sleep(5)
                
                temps = self.extruder.get_temperatures()
                if temps and 'extruder' in temps:
                    current_temp = temps['extruder']['actual']
                    target_temp = temps['extruder']['target']
                    print(f"当前温度: {current_temp:.1f}°C (目标: {target_temp:.1f}°C)")
                
                # 关闭加热器
                print("关闭加热器...")
                self.extruder.turn_off_heaters()
                print("✅ 加热器已关闭")
                
                self.test_results['temperature_control'] = True
                return True
            else:
                print("❌ 温度设置失败")
                return False
                
        except Exception as e:
            print(f"❌ 温度控制测试异常: {e}")
            # 确保关闭加热器
            if self.extruder:
                self.extruder.turn_off_heaters()
            self.test_results['temperature_control'] = False
            return False
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "🛡️"*20)
        print("安全测试总结")
        print("🛡️"*20)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  - {test_name}: {status}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有安全测试通过！硬件连接正常。")
        else:
            print("\n⚠️ 部分测试失败，请检查硬件连接和配置。")
        
        print("🛡️"*20)
    
    def cleanup(self):
        """清理资源"""
        print("\n正在清理资源...")
        
        if self.extruder:
            try:
                self.extruder.turn_off_heaters()
                print("✅ 挤出机加热器已关闭")
            except:
                pass
        
        if self.robot:
            try:
                self.robot.disable_servos()
                self.robot.disconnect()
                print("✅ 机器人连接已断开")
            except:
                pass
        
        print("✅ 资源清理完成")


def main():
    """主函数"""
    tester = SafeHardwareTester()
    
    try:
        # 显示安全信息
        tester.print_safety_banner()
        
        # 确认安全条件
        if not tester.confirm_safety():
            return
        
        # 执行测试
        print("\n开始安全硬件测试...")
        
        # 测试机器人连接
        tester.test_robot_connection()
        
        # 测试安全运动
        tester.test_safe_robot_movement()
        
        # 测试挤出机连接
        tester.test_extruder_connection()
        
        # 测试安全温度控制
        tester.test_safe_temperature_control()
        
        # 打印测试总结
        tester.print_test_summary()
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生异常: {e}")
    finally:
        # 确保资源清理
        tester.cleanup()


if __name__ == "__main__":
    main()
