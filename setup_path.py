"""
Path setup utility for the project.
This module ensures that all Python modules can properly import from the lib directory.
"""

import os
import sys


def setup_project_paths():
    """
    设置项目路径，确保能够正确导入lib目录中的模块
    """
    # 获取项目根目录（当前文件所在目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = current_dir
    
    # lib目录路径
    lib_path = os.path.join(project_root, 'lib')
    
    # 将路径添加到sys.path（如果还没有添加的话）
    paths_to_add = [project_root, lib_path]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    return project_root, lib_path


def get_project_root():
    """
    获取项目根目录路径
    """
    return os.path.dirname(os.path.abspath(__file__))


def get_lib_path():
    """
    获取lib目录路径
    """
    return os.path.join(get_project_root(), 'lib')


# 自动执行路径设置
setup_project_paths()
