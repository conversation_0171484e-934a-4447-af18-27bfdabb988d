# test_basic_api_calls.py
"""
测试基本的API调用，验证连接后的基础功能

这个测试用于诊断为什么get_servo_state等API调用返回-1错误
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface进行低级别测试
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_basic_api_calls():
    """
    测试基本的API调用
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("基本API调用测试")
        print("=" * 60)
        
        # 连接机器人
        print("连接机器人...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败，错误代码: {socket_fd}")
            return False
        
        print(f"[成功] 连接成功，套接字FD: {socket_fd}")
        print("-" * 40)
        
        # 等待连接稳定
        print("等待连接稳定...")
        time.sleep(2)
        
        # 测试1：clear_error
        print("测试1: clear_error")
        try:
            result = nrc.clear_error(socket_fd)
            print(f"clear_error 结果: {result}")
        except Exception as e:
            print(f"clear_error 异常: {e}")

        time.sleep(1)

        # 测试1.5：尝试设置远程模式
        print("\n测试1.5: set_current_mode(远程模式)")
        try:
            result = nrc.set_current_mode(socket_fd, 2)  # 2 = 远程模式
            print(f"set_current_mode(2) 结果: {result}")
        except Exception as e:
            print(f"set_current_mode 异常: {e}")

        time.sleep(1)
        
        # 测试2：get_servo_state - 尝试不同的调用方式
        print("\n测试2: get_servo_state - 方式1")
        try:
            status_value = 0
            result = nrc.get_servo_state(socket_fd, status_value)
            print(f"get_servo_state 结果: {result}")
            print(f"结果类型: {type(result)}")
        except Exception as e:
            print(f"get_servo_state 异常: {e}")
        
        # 测试3：get_servo_state - 尝试使用列表
        print("\n测试3: get_servo_state - 方式2")
        try:
            status_list = [0]
            result = nrc.get_servo_state(socket_fd, status_list)
            print(f"get_servo_state 结果: {result}")
            print(f"status_list: {status_list}")
        except Exception as e:
            print(f"get_servo_state 异常: {e}")
        
        # 测试4：get_robot_running_state
        print("\n测试4: get_robot_running_state")
        try:
            status_value = 0
            result = nrc.get_robot_running_state(socket_fd, status_value)
            print(f"get_robot_running_state 结果: {result}")
        except Exception as e:
            print(f"get_robot_running_state 异常: {e}")
        
        # 测试5：get_current_position
        print("\n测试5: get_current_position")
        try:
            position = [0.0] * 6
            result = nrc.get_current_position(socket_fd, 1, position)
            print(f"get_current_position 结果: {result}")
            print(f"position: {position}")
        except Exception as e:
            print(f"get_current_position 异常: {e}")
        
        # 测试6：set_servo_state
        print("\n测试6: set_servo_state")
        try:
            result = nrc.set_servo_state(socket_fd, 1)
            print(f"set_servo_state(1) 结果: {result}")
        except Exception as e:
            print(f"set_servo_state 异常: {e}")
        
        time.sleep(1)
        
        # 再次测试get_servo_state
        print("\n测试7: 设置后再次get_servo_state")
        try:
            status_value = 0
            result = nrc.get_servo_state(socket_fd, status_value)
            print(f"get_servo_state 结果: {result}")
        except Exception as e:
            print(f"get_servo_state 异常: {e}")
        
        # 测试8：set_servo_poweron
        print("\n测试8: set_servo_poweron")
        try:
            result = nrc.set_servo_poweron(socket_fd)
            print(f"set_servo_poweron 结果: {result}")
        except Exception as e:
            print(f"set_servo_poweron 异常: {e}")
        
        time.sleep(2)
        
        # 最终状态检查
        print("\n测试9: 上电后状态检查")
        try:
            status_value = 0
            result = nrc.get_servo_state(socket_fd, status_value)
            print(f"最终 get_servo_state 结果: {result}")
        except Exception as e:
            print(f"最终 get_servo_state 异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            print("-" * 40)
            print("正在安全关闭...")
            
            try:
                # 尝试下电
                print("尝试下电...")
                nrc.set_servo_poweroff(socket_fd)
                time.sleep(1)
                
                # 断开连接
                print("断开连接...")
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except Exception as e:
                print(f"[警告] 关闭过程中发生异常: {e}")


def test_api_documentation():
    """
    测试API文档中的示例调用方式
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("API文档示例调用方式测试")
        print("=" * 60)
        
        # 连接
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败")
            return False
        
        print(f"[成功] 连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        # 按照官方文档的建议顺序
        print("1. 清除错误")
        result = nrc.clear_error(socket_fd)
        print(f"clear_error: {result}")
        
        print("2. 检查伺服状态")
        # 尝试不同的参数类型
        for i, param_type in enumerate(["int", "list"]):
            print(f"  尝试 {param_type} 参数:")
            try:
                if param_type == "int":
                    status = 0
                else:
                    status = [0]
                
                result = nrc.get_servo_state(socket_fd, status)
                print(f"    结果: {result}")
                print(f"    状态参数: {status}")
                
                if isinstance(result, list) and len(result) >= 2:
                    print(f"    解析: 返回码={result[0]}, 状态值={result[1]}")
                    break
            except Exception as e:
                print(f"    异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"文档测试中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                nrc.disconnect_robot(socket_fd)
            except:
                pass


def main():
    """
    主函数
    """
    print("INEXBOT 基本API调用诊断测试")
    print()
    print("这个测试用于诊断API调用返回-1错误的原因")
    print()
    
    print("测试选项:")
    print("1. 基本API调用测试")
    print("2. API文档示例测试")
    print("3. 两个测试都运行")
    
    choice = input("\n请选择测试 (1/2/3): ").strip()
    
    if choice == "1":
        test_basic_api_calls()
    elif choice == "2":
        test_api_documentation()
    elif choice == "3":
        print("\n运行基本API调用测试...")
        test_basic_api_calls()
        
        print("\n" + "="*60)
        print("运行API文档示例测试...")
        test_api_documentation()
    else:
        print("无效选择，退出测试")


if __name__ == "__main__":
    main()
