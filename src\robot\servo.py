# src/robot/servo.py
"""
伺服控制专用模块

该模块专门负责机器人伺服电机的控制，包括使能、关闭、上电、下电等操作。
实现了根据INEXBOT官方文档的增强伺服控制功能。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
# 添加lib目录到Python路径
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
except ImportError:
    nrc = None

import time
from ..core.validators import ParameterValidator


class ServoController:
    """
    伺服控制器
    
    专门负责机器人伺服电机的控制，实现了增强的伺服控制功能。
    """
    
    def __init__(self, connection_manager, error_handler):
        """
        初始化伺服控制器
        
        Args:
            connection_manager: 连接管理器实例
            error_handler: 错误处理器实例
        """
        self.conn = connection_manager
        self.error_handler = error_handler
    
    def enable_servos(self):
        """
        打开机器人伺服电机

        根据INEXBOT官方文档的正确调用顺序：
        1. 首先调用 clear_error() 清除所有历史报警
        2. 然后再尝试调用 set_servo_poweron()
        3. 只有在 set_servo_poweron() 成功返回后，再调用 get_servo_state()、get_current_position() 等函数

        Returns:
            bool: 使能成功返回True，失败返回False
        """
        if self.conn.is_development_mode():
            print("[提示] 开发模式：模拟伺服使能")
            return True

        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法使能伺服")
            return False

        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False

        print("正在执行正确的伺服使能流程...")

        # 步骤1：首先调用 clear_error() 清除所有历史报警
        print("步骤1: 清除所有历史报警")
        try:
            result = nrc.clear_error(self.conn.get_socket_fd())
            if result == 0:
                print("[成功] 历史报警清除成功")
            else:
                print(f"[警告] 清除历史报警失败，错误代码: {result}，但继续尝试")
        except Exception as e:
            print(f"[警告] 清除历史报警异常: {e}，但继续尝试")

        time.sleep(1)  # 等待错误清除生效

        # 步骤2：然后再尝试调用 set_servo_poweron()
        print("步骤2: 尝试机器人上电")
        try:
            result = nrc.set_servo_poweron(self.conn.get_socket_fd())
            if result == 0:
                print("[成功] 机器人上电成功")
            else:
                print(f"[错误] 机器人上电失败，错误代码: {result}")
                return False
        except Exception as e:
            print(f"[错误] 机器人上电异常: {e}")
            return False

        time.sleep(2)  # 等待上电完成

        # 步骤3：只有在 set_servo_poweron() 成功返回后，再调用状态查询函数
        print("步骤3: 验证上电后的伺服状态")
        try:
            servo_state = self.get_servo_state()
            if servo_state is None:
                print("[警告] 无法获取伺服状态，但上电命令已成功")
                return True  # 上电成功，即使无法验证状态

            status_code = servo_state['status_code']
            description = servo_state['description']
            print(f"上电后伺服状态: {description} (代码: {status_code})")

            # 根据官方文档，上电成功后应该是运行状态(3)
            if status_code == 3:
                print("[成功] 伺服使能完成，机器人处于运行状态")
                return True
            elif status_code == 1:
                print("[成功] 伺服使能完成，机器人处于就绪状态")
                return True
            else:
                print(f"[警告] 伺服状态异常: {description}，但上电命令已成功")
                return True  # 上电成功，即使状态异常

        except Exception as e:
            print(f"[警告] 验证伺服状态异常: {e}，但上电命令已成功")
            return True  # 上电成功，即使无法验证

        return True
    
    def disable_servos(self):
        """
        关闭机器人伺服电机
        
        增强的伺服关闭方法：
        1. 检查当前伺服状态
        2. 如果处于运行状态，先下电再关闭伺服
        3. 验证关闭结果
        4. 提供详细的状态反馈
        
        Returns:
            bool: 关闭成功返回True，失败返回False
        """
        if self.conn.is_development_mode():
            print("[提示] 开发模式：模拟伺服关闭")
            return True
        
        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法关闭伺服")
            return False
        
        print("正在执行增强的伺服关闭流程...")
        
        # 步骤1：获取当前伺服状态
        print("步骤1: 检查当前伺服状态")
        current_servo_state = self.get_servo_state()
        if current_servo_state is None:
            print("[警告] 无法获取当前伺服状态，尝试直接关闭")
        else:
            status_code = current_servo_state['status_code']
            description = current_servo_state['description']
            print(f"当前伺服状态: {description} (代码: {status_code})")
            
            # 如果已经是停止状态，无需重复关闭
            if status_code == 0:
                print("[成功] 伺服已处于停止状态，无需重复关闭")
                return True
            
            # 如果处于运行状态，建议先下电
            if status_code == 3:
                print("步骤2: 检测到运行状态，先执行下电操作")
                if self.set_servo_poweroff():
                    print("[成功] 下电成功，继续关闭伺服")
                    time.sleep(1)  # 等待下电完成
                else:
                    print("[警告] 下电失败，但继续尝试关闭伺服")
        
        # 步骤3：设置伺服为停止状态
        print("步骤3: 设置伺服为停止状态")
        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False
        
        try:
            result = nrc.set_servo_state(self.conn.get_socket_fd(), 0)
            if result != 0:
                print(f"[错误] 伺服关闭失败，错误代码: {result}")
                return False
        except Exception as e:
            print(f"[错误] 伺服关闭异常: {e}")
            return False
        
        # 步骤4：验证关闭结果
        print("步骤4: 验证伺服关闭结果")
        time.sleep(1)  # 等待状态更新
        
        final_servo_state = self.get_servo_state()
        if final_servo_state is None:
            print("[警告] 无法验证伺服关闭结果")
            return False
        
        final_status_code = final_servo_state['status_code']
        final_description = final_servo_state['description']
        
        if final_status_code == 0:
            print(f"[成功] 伺服关闭成功: {final_description}")
            return True
        else:
            print(f"[警告] 伺服关闭验证异常: {final_description} (代码: {final_status_code})")
            # 即使状态不是0，只要不是错误状态，也可能是正常的
            return final_status_code != 2  # 只要不是报警状态就认为成功

    def get_servo_state(self):
        """
        获取伺服电机状态

        正确处理C++接口的输出参数，按照官方文档规范。

        Returns:
            dict: 包含伺服状态信息的字典，失败返回None
            格式: {"enabled": bool, "status_code": int, "description": str}
        """
        if self.conn.is_development_mode():
            return {
                "enabled": True,
                "status_code": 1,
                "description": "就绪状态"
            }

        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法获取伺服状态")
            return None

        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return None

        try:
            # 根据C++接口文档: Result get_servo_state(SOCKETFD socketFd, int &status)
            # Python包装器返回 [result_code, status_value]
            status_value = 0  # 初始化输出参数

            # 调用API获取伺服状态 - 返回值是列表 [result_code, actual_status]
            result_list = nrc.get_servo_state(self.conn.get_socket_fd(), status_value)

            if isinstance(result_list, list) and len(result_list) >= 2:
                result_code = result_list[0]
                status_code = result_list[1]

                if result_code == 0:  # SUCCESS
                    # 根据官方文档的伺服状态定义
                    servo_descriptions = {
                        0: "停止状态",
                        1: "就绪状态",
                        2: "报警状态",
                        3: "运行状态"
                    }

                    description = servo_descriptions.get(status_code, f"未知状态({status_code})")
                    enabled = status_code in [1, 3]  # 就绪或运行状态视为已使能

                    return {
                        "enabled": enabled,
                        "status_code": status_code,
                        "description": description
                    }
                else:
                    print(f"[错误] 获取伺服状态失败，错误代码: {result_code}")
                    return None
            else:
                print("[错误] 获取伺服状态失败：返回数据格式错误")
                return None

        except Exception as e:
            print(f"[错误] 获取伺服状态异常: {e}")
            return None

    def check_servo_ready_for_motion(self):
        """
        检查伺服状态是否适合执行运动操作

        根据INEXBOT官方文档，只有在伺服状态为就绪(1)或运行(3)时才能执行运动操作。

        Returns:
            bool: 适合运动返回True，不适合返回False
        """
        servo_state = self.get_servo_state()
        if servo_state is None:
            print("[错误] 无法获取伺服状态，不建议执行运动")
            return False

        status_code = servo_state['status_code']
        description = servo_state['description']

        if status_code in [1, 3]:  # 就绪状态或运行状态
            print(f"[成功] 伺服状态适合运动: {description}")
            return True
        elif status_code == 0:  # 停止状态
            print(f"[警告] 伺服处于停止状态，需要先使能: {description}")
            return False
        elif status_code == 2:  # 报警状态
            print(f"[错误] 伺服处于报警状态，需要先清除错误: {description}")
            return False
        else:
            print(f"[警告] 伺服状态未知，不建议执行运动: {description}")
            return False

    def set_servo_poweron(self):
        """
        机器人上电

        根据官方文档：调用该函数之前需要先调用set_servo_state(1)将伺服设置为就绪状态，
        机器人上电成功后调用get_servo_state()为3伺服运行状态。

        Returns:
            bool: 上电成功返回True，失败返回False
        """
        if self.conn.is_development_mode():
            print("[提示] 开发模式：模拟机器人上电")
            return True

        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法上电")
            return False

        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False

        try:
            print("正在执行机器人上电...")
            result = nrc.set_servo_poweron(self.conn.get_socket_fd())

            if result == 0:  # SUCCESS
                print("[成功] 机器人上电成功")
                return True
            else:
                print(f"[错误] 机器人上电失败，错误代码: {result}")
                return False

        except Exception as e:
            print(f"[错误] 机器人上电异常: {e}")
            return False

    def set_servo_poweroff(self):
        """
        机器人下电

        根据官方文档：机器人下电成功后调用get_servo_state()为1伺服就绪状态，
        该函数只有伺服状态为3（运行状态）时调用生效。

        Returns:
            bool: 下电成功返回True，失败返回False
        """
        if self.conn.is_development_mode():
            print("[提示] 开发模式：模拟机器人下电")
            return True

        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法下电")
            return False

        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False

        try:
            print("正在执行机器人下电...")
            result = nrc.set_servo_poweroff(self.conn.get_socket_fd())

            if result == 0:  # SUCCESS
                print("[成功] 机器人下电成功")
                return True
            else:
                print(f"[错误] 机器人下电失败，错误代码: {result}")
                return False

        except Exception as e:
            print(f"[错误] 机器人下电异常: {e}")
            return False
