# src/core/safety_validator.py
"""
安全验证模块

该模块提供硬件测试时的安全验证功能，确保所有操作参数都在安全范围内。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
import time
import logging

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(project_root)

try:
    from config import *
except ImportError:
    print("[警告] 无法导入配置文件，使用默认安全配置")
    SAFE_MODE = True
    MAX_VELOCITY = 20.0
    MOVEMENT_LIMIT = 50.0
    MAX_EXTRUDER_TEMPERATURE = 200.0


class SafetyValidator:
    """
    安全验证器
    
    提供各种安全检查和参数验证功能，确保硬件操作的安全性。
    """
    
    def __init__(self):
        """初始化安全验证器"""
        self.error_count = 0
        self.last_error_time = 0
        self.safety_log = []
        
        # 设置安全日志
        if globals().get('LOG_SAFETY_EVENTS', True):
            logging.basicConfig(
                filename=globals().get('SAFETY_LOG_FILE', 'safety.log'),
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
    
    def is_safe_mode_enabled(self):
        """检查是否启用安全模式"""
        return globals().get('SAFE_MODE', True)
    
    def validate_movement_parameters(self, target_pos, velocity, blending_radius):
        """
        验证运动参数的安全性
        
        Args:
            target_pos (list): 目标位置 [X,Y,Z,A,B,C]
            velocity (float): 运动速度
            blending_radius (float): 路径平滑半径
            
        Returns:
            tuple: (is_safe, adjusted_params, warnings)
        """
        if not self.is_safe_mode_enabled():
            return True, (target_pos, velocity, blending_radius), []
        
        warnings = []
        adjusted_pos = target_pos.copy()
        adjusted_velocity = velocity
        adjusted_blending = blending_radius
        
        # 验证速度
        max_vel = globals().get('MAX_VELOCITY', 20.0)
        min_vel = globals().get('MIN_VELOCITY', 1.0)
        
        if velocity > max_vel:
            adjusted_velocity = max_vel
            warnings.append(f"速度限制：{velocity} -> {max_vel} mm/s")
        elif velocity < min_vel:
            adjusted_velocity = min_vel
            warnings.append(f"速度调整：{velocity} -> {min_vel} mm/s")
        
        # 验证工作空间限制
        workspace_limits = globals().get('WORKSPACE_LIMITS', {})
        axes = ['X', 'Y', 'Z', 'A', 'B', 'C']
        
        for i, axis in enumerate(axes):
            if axis in workspace_limits:
                limits = workspace_limits[axis]
                if adjusted_pos[i] < limits['min']:
                    adjusted_pos[i] = limits['min']
                    warnings.append(f"{axis}轴位置限制：{target_pos[i]} -> {limits['min']}")
                elif adjusted_pos[i] > limits['max']:
                    adjusted_pos[i] = limits['max']
                    warnings.append(f"{axis}轴位置限制：{target_pos[i]} -> {limits['max']}")
        
        # 验证路径平滑半径
        safe_blending = globals().get('SAFE_BLENDING_RADIUS', 1.0)
        if blending_radius > safe_blending:
            adjusted_blending = safe_blending
            warnings.append(f"路径平滑半径限制：{blending_radius} -> {safe_blending}")
        
        # 记录安全事件
        if warnings:
            self._log_safety_event("MOVEMENT_ADJUSTMENT", warnings)
        
        return True, (adjusted_pos, adjusted_velocity, adjusted_blending), warnings
    
    def validate_temperature(self, temperature, heater_type="extruder"):
        """
        验证温度参数的安全性
        
        Args:
            temperature (float): 目标温度
            heater_type (str): 加热器类型 ("extruder" 或 "bed")
            
        Returns:
            tuple: (is_safe, adjusted_temp, warnings)
        """
        if not self.is_safe_mode_enabled():
            return True, temperature, []
        
        warnings = []
        adjusted_temp = temperature
        
        # 根据加热器类型设置限制
        if heater_type == "extruder":
            max_temp = globals().get('MAX_EXTRUDER_TEMPERATURE', 200.0)
        elif heater_type == "bed":
            max_temp = globals().get('MAX_BED_TEMPERATURE', 60.0)
        else:
            max_temp = 100.0  # 默认限制
        
        min_temp = globals().get('MIN_TEMPERATURE', 15.0)
        
        if temperature > max_temp:
            adjusted_temp = max_temp
            warnings.append(f"{heater_type}温度限制：{temperature}°C -> {max_temp}°C")
        elif temperature < min_temp:
            adjusted_temp = min_temp
            warnings.append(f"{heater_type}温度调整：{temperature}°C -> {min_temp}°C")
        
        # 记录安全事件
        if warnings:
            self._log_safety_event("TEMPERATURE_ADJUSTMENT", warnings)
        
        return True, adjusted_temp, warnings
    
    def validate_extrude_parameters(self, amount, speed):
        """
        验证挤出参数的安全性
        
        Args:
            amount (float): 挤出量
            speed (float): 挤出速度
            
        Returns:
            tuple: (is_safe, adjusted_params, warnings)
        """
        if not self.is_safe_mode_enabled():
            return True, (amount, speed), []
        
        warnings = []
        adjusted_amount = amount
        adjusted_speed = speed
        
        # 验证挤出量
        max_amount = globals().get('MAX_EXTRUDE_AMOUNT', 10.0)
        if amount > max_amount:
            adjusted_amount = max_amount
            warnings.append(f"挤出量限制：{amount}mm -> {max_amount}mm")
        
        # 验证挤出速度
        max_speed = globals().get('MAX_EXTRUDE_SPEED', 300)
        min_speed = globals().get('MIN_EXTRUDE_SPEED', 60)
        
        if speed > max_speed:
            adjusted_speed = max_speed
            warnings.append(f"挤出速度限制：{speed} -> {max_speed} mm/min")
        elif speed < min_speed:
            adjusted_speed = min_speed
            warnings.append(f"挤出速度调整：{speed} -> {min_speed} mm/min")
        
        # 记录安全事件
        if warnings:
            self._log_safety_event("EXTRUDE_ADJUSTMENT", warnings)
        
        return True, (adjusted_amount, adjusted_speed), warnings
    
    def check_error_threshold(self):
        """
        检查错误次数是否超过阈值
        
        Returns:
            bool: True表示可以继续操作，False表示需要停止
        """
        max_errors = globals().get('MAX_ERROR_COUNT', 3)
        cooldown_time = globals().get('ERROR_COOLDOWN_TIME', 10)
        
        current_time = time.time()
        
        # 如果在冷却期内，不允许操作
        if current_time - self.last_error_time < cooldown_time:
            return False
        
        # 如果错误次数超过阈值，需要重置
        if self.error_count >= max_errors:
            print(f"[安全警告] 错误次数达到上限({max_errors})，需要人工确认后才能继续")
            return False
        
        return True
    
    def record_error(self):
        """记录错误事件"""
        self.error_count += 1
        self.last_error_time = time.time()
        self._log_safety_event("ERROR_RECORDED", [f"错误计数: {self.error_count}"])
    
    def reset_error_count(self):
        """重置错误计数（需要人工确认）"""
        self.error_count = 0
        self.last_error_time = 0
        self._log_safety_event("ERROR_RESET", ["错误计数已重置"])
        print("[安全信息] 错误计数已重置")
    
    def get_safe_test_parameters(self):
        """
        获取安全的测试参数
        
        Returns:
            dict: 安全的测试参数
        """
        return {
            'movement_distance': globals().get('TEST_MOVEMENT_DISTANCE', 5.0),
            'velocity': globals().get('TEST_VELOCITY', 10.0),
            'temperature': globals().get('TEST_TEMPERATURE', 180.0),
            'blending_radius': globals().get('SAFE_BLENDING_RADIUS', 1.0),
            'extrude_amount': 2.0,  # 安全的测试挤出量
            'extrude_speed': 100    # 安全的测试挤出速度
        }
    
    def _log_safety_event(self, event_type, details):
        """
        记录安全事件
        
        Args:
            event_type (str): 事件类型
            details (list): 事件详情
        """
        if globals().get('LOG_SAFETY_EVENTS', True):
            event = {
                'timestamp': time.time(),
                'type': event_type,
                'details': details
            }
            self.safety_log.append(event)
            
            # 写入日志文件
            logging.info(f"{event_type}: {'; '.join(details)}")
    
    def print_safety_summary(self):
        """打印安全配置摘要"""
        print("\n" + "="*60)
        print("安全配置摘要")
        print("="*60)
        print(f"安全模式: {'启用' if self.is_safe_mode_enabled() else '禁用'}")
        print(f"最大运动速度: {globals().get('MAX_VELOCITY', 20.0)} mm/s")
        print(f"最大挤出头温度: {globals().get('MAX_EXTRUDER_TEMPERATURE', 200.0)}°C")
        print(f"最大热床温度: {globals().get('MAX_BED_TEMPERATURE', 60.0)}°C")
        print(f"单次移动限制: {globals().get('MOVEMENT_LIMIT', 50.0)} mm")
        print(f"错误计数: {self.error_count}/{globals().get('MAX_ERROR_COUNT', 3)}")
        print("="*60)


# 全局安全验证器实例
safety_validator = SafetyValidator()
