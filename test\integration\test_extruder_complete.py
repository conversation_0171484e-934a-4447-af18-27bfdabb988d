# test_extruder.py
"""
Klipper挤出机测试脚本

该脚本用于安全地测试Klipper挤出机的基本功能，包括：
- 连接到Moonraker API
- 温度读取和控制
- 挤出机加热
- 挤出动作

重要提示：运行前请确保：
1. Klipper和Moonraker服务正在运行
2. 打印机已装载耗材
3. 喷嘴周围有足够的安全空间
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from hardware.extruder_interface import ExtruderInterface

# 导入配置
try:
    from config.robot_config import KLIPPER_IP
except ImportError:
    try:
        from config import KLIPPER_IP
    except ImportError:
        # 如果config模块导入失败，使用默认值
        KLIPPER_IP = "*************"


def run_extruder_test():
    """
    执行一个挤出机加热和挤出的测试序列。
    
    测试流程：
    1. 连接到Klipper/Moonraker
    2. 检查当前温度状态
    3. 设置目标温度并等待加热
    4. 执行少量挤出测试
    5. 关闭加热器
    """
    extruder = None  # 初始化变量
    
    try:
        print("=" * 60)
        print("Klipper挤出机控制测试 - 第三步")
        print("=" * 60)
        
        # 1. 连接到Klipper
        print(f"正在连接到Klipper/Moonraker at {KLIPPER_IP}...")
        extruder = ExtruderInterface(KLIPPER_IP)
        
        if not extruder.get_status():
            raise ConnectionError("无法连接到Klipper，请检查IP地址和Moonraker服务。")
        
        print("Klipper连接成功。")
        print("-" * 40)
        
        # 2. 检查当前温度状态
        print("正在检查当前温度状态...")
        temp_info = extruder.get_temperatures()
        
        if temp_info is None:
            print("警告：无法获取温度信息")
        else:
            if "extruder" in temp_info:
                ext_temp = temp_info["extruder"]
                print(f"挤出头温度: {ext_temp['actual']:.1f}°C / {ext_temp['target']:.1f}°C")
            
            if "heater_bed" in temp_info:
                bed_temp = temp_info["heater_bed"]
                print(f"热床温度: {bed_temp['actual']:.1f}°C / {bed_temp['target']:.1f}°C")
        
        print("-" * 40)
        
        # 3. 设置目标温度并等待
        # !!! 警告: 请根据您的耗材类型设置安全的目标温度 !!!
        # PLA: 190-210°C, PETG: 220-250°C, ABS: 230-260°C
        target_temp = 190.0
        
        print(f"准备设置挤出头目标温度为 {target_temp}°C")
        print("请确认：")
        print("1. 打印机已装载合适的耗材")
        print("2. 喷嘴周围没有障碍物")
        print("3. 目标温度适合当前耗材类型")
        print()
        
        user_input = input("确认安全条件后，输入 'yes' 开始加热: ")
        if user_input.lower() != 'yes':
            print("测试已取消")
            return
        
        print(f"\n正在设置目标温度为 {target_temp}°C...")
        if not extruder.set_heater_temperature(target_temp):
            raise RuntimeError("设置目标温度失败")
        
        print("开始等待加热...")
        extruder.wait_for_temperature(target_temp)
        print("已达到目标温度！")
        print("-" * 40)
        
        # 4. 执行挤出测试
        # !!! 警告: 执行前请确保喷嘴上方有足够空间，且打印机内已装载耗材 !!!
        extrude_amount = 5.0  # mm
        extrude_speed = 300   # mm/min
        
        print(f"准备执行挤出测试:")
        print(f"  挤出量: {extrude_amount} mm")
        print(f"  挤出速度: {extrude_speed} mm/min")
        print()
        print("请确认：")
        print("1. 喷嘴下方有足够空间接收挤出的耗材")
        print("2. 挤出机内已正确装载耗材")
        print("3. 温度已稳定在目标值")
        print()
        
        user_input = input("确认准备就绪后，按回车键开始挤出...")
        
        print(f"\n正在执行挤出: {extrude_amount}mm @ {extrude_speed}mm/min...")
        if not extruder.extrude(extrude_amount, extrude_speed):
            raise RuntimeError("挤出指令发送失败")
        
        print("挤出指令已发送。")
        print("等待挤出完成...")
        time.sleep(5)  # 等待挤出完成
        
        print("挤出测试完成！")
        print("-" * 40)
        
        # 5. 可选：执行回抽测试
        print("可选：执行回抽测试以防止耗材滴落")
        user_input = input("是否执行回抽？输入 'yes' 执行回抽: ")
        
        if user_input.lower() == 'yes':
            retract_amount = -2.0  # 负值表示回抽
            retract_speed = 600    # 回抽速度通常更快
            
            print(f"正在执行回抽: {abs(retract_amount)}mm @ {retract_speed}mm/min...")
            if extruder.extrude(retract_amount, retract_speed):
                print("回抽完成。")
            else:
                print("回抽失败。")
        
        print("-" * 40)
        print("所有测试完成！")
        
    except ConnectionError as e:
        print(f"连接错误: {e}")
        print("请检查：")
        print("1. Klipper服务是否正在运行")
        print("2. Moonraker服务是否正在运行")
        print("3. config.py中的KLIPPER_IP是否正确")
        print("4. 网络连接是否正常")
        
    except TimeoutError as e:
        print(f"超时错误: {e}")
        print("请检查：")
        print("1. 加热器是否正常工作")
        print("2. 温度传感器是否正常")
        print("3. 目标温度是否合理")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        
    finally:
        # 6. 确保关闭加热器
        if extruder:
            print("-" * 40)
            print("测试结束，正在关闭加热器...")
            extruder.turn_off_heaters()
            print("加热器已关闭。")
            
            # 最终温度检查
            time.sleep(2)
            final_temp = extruder.get_temperatures()
            if final_temp and "extruder" in final_temp:
                print(f"最终挤出头目标温度: {final_temp['extruder']['target']:.1f}°C")
        
        print("=" * 60)
        print("测试完成")
        print("=" * 60)


def main():
    """
    主函数
    """
    print("警告：运行此测试前请确保：")
    print("1. Klipper和Moonraker服务正在运行")
    print("2. 打印机已装载合适的耗材")
    print("3. 喷嘴周围有足够的安全空间")
    print("4. 了解当前耗材的安全加热温度")
    print("5. 有紧急停止按钮可用")
    print()
    
    # 等待用户确认
    user_input = input("确认所有安全条件后，输入 'yes' 继续测试: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return
    
    run_extruder_test()


if __name__ == "__main__":
    main()
