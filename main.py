# main.py
"""
机器人3D打印控制系统主程序入口

该文件是应用程序的启动脚本，用于测试机器人连接功能。
在后续开发中，这里将成为整个系统的入口点。
"""

import sys
import time
from config import ROBOT_IP, ROBOT_PORT, DEBUG_MODE
from hardware.robot_interface import RobotInterface


def main():
    """
    主程序函数
    
    测试机器人连接和基本功能
    """
    robot = None
    
    try:
        print("=" * 50)
        print("机器人3D打印控制系统 - 第一步测试")
        print("=" * 50)
        
        # 显示配置信息
        if DEBUG_MODE:
            print(f"机器人IP: {ROBOT_IP}")
            print(f"机器人端口: {ROBOT_PORT}")
            print(f"调试模式: 已启用")
            print("-" * 30)
        
        # 尝试连接机器人
        print("正在尝试连接机器人...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        
        # 连接成功后的验证
        if robot.is_robot_connected():
            print("机器人连接成功！")
            
            # 获取并显示连接状态
            status = robot.get_connection_status()
            print(f"当前连接状态: {status}")
            
            # 获取套接字文件描述符
            socket_fd = robot.get_socket_fd()
            print(f"套接字文件描述符: {socket_fd}")
            
            # 保持连接一段时间以验证稳定性
            print("保持连接5秒钟以验证稳定性...")
            time.sleep(5)
            
            # 再次检查连接状态
            final_status = robot.get_connection_status()
            print(f"5秒后连接状态: {final_status}")
            
            print("机器人连接测试完成！")
        else:
            print("错误：机器人连接验证失败")
            sys.exit(1)
            
    except ConnectionError as e:
        print(f"连接错误: {e}")
        print("请检查：")
        print("1. 机器人是否已开机并正常运行")
        print("2. 网络连接是否正常")
        print("3. config.py中的IP地址和端口是否正确")
        sys.exit(1)
        
    except Exception as e:
        print(f"未预期的错误: {e}")
        sys.exit(1)
        
    finally:
        # 确保无论程序是否发生错误，都能最终执行断开连接的操作
        if robot is not None:
            print("-" * 30)
            print("正在清理资源...")
            robot.disconnect()
            print("资源清理完成")
        
        print("=" * 50)
        print("程序结束")
        print("=" * 50)


if __name__ == "__main__":
    main()
