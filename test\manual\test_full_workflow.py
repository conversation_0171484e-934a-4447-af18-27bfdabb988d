# test_basic_api.py
"""
机器人基础API测试脚本

该脚本用于测试机器人连接后的基础API调用，
直接使用nrc_interface进行低级别测试。
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface进行低级别测试
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_direct_api():
    """
    直接测试nrc_interface API
    """
    socket_fd = None

    try:
        print("=" * 60)
        print("机器人基础API测试")
        print("=" * 60)

        # 1. 连接机器人
        print("步骤1: 直接连接机器人")
        print(f"正在连接 {ROBOT_IP}:{ROBOT_PORT}...")

        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败，错误代码: {socket_fd}")
            return False

        print(f"[成功] 连接成功，套接字FD: {socket_fd}")
        print("-" * 40)

        # 2. 测试连接状态
        print("步骤2: 测试连接状态")
        conn_status = nrc.get_connection_status(socket_fd)
        print(f"连接状态: {conn_status}")
        print("-" * 40)

        # 3. 测试获取运行状态 - 使用不同的调用方式
        print("步骤3: 测试获取运行状态")

        # 方式1: 直接调用
        try:
            status_value = 0
            result = nrc.get_robot_running_state(socket_fd, status_value)
            print(f"方式1 - 直接调用结果: {result}")
            print(f"类型: {type(result)}")
        except Exception as e:
            print(f"方式1 - 调用异常: {e}")

        # 方式2: 使用ctypes.byref（如果需要）
        try:
            import ctypes
            status_ref = ctypes.c_int(0)
            result2 = nrc.get_robot_running_state(socket_fd, status_ref)
            print(f"方式2 - ctypes调用结果: {result2}")
            print(f"状态值: {status_ref.value}")
        except Exception as e:
            print(f"方式2 - 调用异常: {e}")

        print("-" * 40)

        # 4. 测试获取伺服状态
        print("步骤4: 测试获取伺服状态")

        try:
            status_value = 0
            result = nrc.get_servo_state(socket_fd, status_value)
            print(f"伺服状态调用结果: {result}")
            print(f"类型: {type(result)}")
        except Exception as e:
            print(f"获取伺服状态异常: {e}")

        print("-" * 40)

        # 5. 测试清除错误
        print("步骤5: 测试清除错误")

        try:
            result = nrc.clear_error(socket_fd)
            print(f"清除错误结果: {result}")
            if result == 0:
                print("[成功] 错误清除成功")
            else:
                print(f"[警告] 清除错误失败，代码: {result}")
        except Exception as e:
            print(f"清除错误异常: {e}")

        print("-" * 40)

        # 6. 测试设置伺服状态
        print("步骤6: 测试设置伺服状态")

        try:
            # 尝试设置伺服为就绪状态(1)
            result = nrc.set_servo_state(socket_fd, 1)
            print(f"设置伺服状态结果: {result}")
            if result == 0:
                print("[成功] 伺服状态设置成功")

                # 等待一下再检查状态
                time.sleep(1)

                # 再次检查伺服状态
                status_value = 0
                result = nrc.get_servo_state(socket_fd, status_value)
                print(f"设置后伺服状态: {result}")
            else:
                print(f"[警告] 伺服状态设置失败，代码: {result}")
        except Exception as e:
            print(f"设置伺服状态异常: {e}")

        print("-" * 40)

        # 7. 测试获取位置
        print("步骤7: 测试获取位置")

        try:
            # 尝试获取直角坐标系位置
            position = [0.0] * 6  # 初始化位置数组
            result = nrc.get_current_position(socket_fd, 1, position)
            print(f"获取位置结果: {result}")
            print(f"位置数据: {position}")
            if result == 0:
                print(f"[成功] 当前位置: {position}")
            else:
                print(f"[警告] 获取位置失败，代码: {result}")
        except Exception as e:
            print(f"获取位置异常: {e}")

        print("-" * 40)

        # 8. 测试速度控制
        print("步骤8: 测试速度控制")

        try:
            # 获取当前速度
            speed_value = 0
            result = nrc.get_speed(socket_fd, speed_value)
            print(f"获取速度结果: {result}")
            if result == 0:
                print(f"[成功] 当前速度: {speed_value}%")
            else:
                print(f"[警告] 获取速度失败，代码: {result}")

            # 设置速度
            result = nrc.set_speed(socket_fd, 50)
            print(f"设置速度结果: {result}")
            if result == 0:
                print("[成功] 速度设置成功")
            else:
                print(f"[警告] 速度设置失败，代码: {result}")
        except Exception as e:
            print(f"速度控制异常: {e}")

        print("-" * 40)
        print("基础API测试完成")
        return True

    except Exception as e:
        print(f"基础API测试过程中发生错误: {e}")
        return False

    finally:
        if socket_fd is not None:
            print("-" * 40)
            print("正在断开连接...")

            try:
                # 尝试关闭伺服
                print("尝试关闭伺服...")
                nrc.set_servo_state(socket_fd, 0)  # 设置为停止状态
                time.sleep(1)

                # 断开连接
                print("断开连接...")
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except Exception as e:
                print(f"[警告] 断开连接时发生异常: {e}")

        print("=" * 60)
        print("基础API测试完成")
        print("=" * 60)


def test_high_level_interface():
    """
    测试高级接口
    """
    from src.robot.interface import RobotInterface

    robot = None
    try:
        print("=" * 60)
        print("高级接口测试")
        print("=" * 60)

        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        # 测试状态获取
        print("测试状态获取...")
        running_state = robot.get_robot_running_state()
        print(f"运行状态: {running_state}")

        servo_state = robot.get_servo_state()
        print(f"伺服状态: {servo_state}")

        return True

    except Exception as e:
        print(f"高级接口测试失败: {e}")
        return False
    finally:
        if robot:
            robot.disconnect()


def main():
    """
    主函数
    """
    print("机器人API测试")
    print("该测试将分别测试低级API和高级接口")
    print()

    user_input = input("确认开始测试，输入 'yes' 继续: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return

    # 先测试低级API
    print("\n开始低级API测试...")
    if test_direct_api():
        print("\n低级API测试成功！")
    else:
        print("\n低级API测试失败！")

    time.sleep(2)

    # 再测试高级接口
    print("\n开始高级接口测试...")
    if test_high_level_interface():
        print("\n高级接口测试成功！")
    else:
        print("\n高级接口测试失败！")


if __name__ == "__main__":
    main()
