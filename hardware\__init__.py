# hardware/__init__.py
"""
硬件通信层模块

该模块封装与具体硬件（机器人、挤出机）的所有通信细节，
为上层提供统一的硬件抽象接口。

主要功能：
- RobotInterface: INEXBOT机械臂控制接口
  * 连接管理和状态查询
  * 伺服控制和运动控制
  * 错误处理和速度控制
  * 支持线性运动和关节运动

- ExtruderInterface: Klipper挤出机控制接口
  * HTTP通信和状态监控
  * 温度管理和加热控制
  * 挤出控制和G-code发送
"""

from .robot_interface import RobotInterface
from .extruder_interface import ExtruderInterface

__all__ = ['RobotInterface', 'ExtruderInterface']

# 版本信息
__version__ = "1.1.0"

# 功能特性标识
FEATURES = {
    'robot_enhanced_control': True,
    'extruder_temperature_control': True,
    'error_handling': True,
    'speed_control': True,
    'joint_motion': True,
    'status_monitoring': True
}
