#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全配置检查脚本

该脚本用于检查和验证安全配置是否正确设置，
并提供配置建议和安全提醒。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from config import *
    from src.core.safety_validator import safety_validator
    config_loaded = True
except ImportError as e:
    print(f"❌ 配置加载失败: {e}")
    config_loaded = False


def check_basic_config():
    """检查基础配置"""
    print("="*60)
    print("基础配置检查")
    print("="*60)
    
    issues = []
    
    # 检查安全模式
    safe_mode = globals().get('SAFE_MODE', False)
    if safe_mode:
        print("✅ 安全模式: 已启用")
    else:
        print("⚠️ 安全模式: 未启用")
        issues.append("建议启用安全模式 (SAFE_MODE = True)")
    
    # 检查IP配置
    robot_ip = globals().get('ROBOT_IP', '')
    klipper_ip = globals().get('KLIPPER_IP', '')
    
    if robot_ip and robot_ip != "************":
        print(f"✅ 机器人IP: {robot_ip}")
    else:
        print("⚠️ 机器人IP: 使用默认值，请确认是否正确")
        issues.append("请确认机器人IP地址是否正确")
    
    if klipper_ip and klipper_ip != "************":
        print(f"✅ 挤出机IP: {klipper_ip}")
    else:
        print("⚠️ 挤出机IP: 使用默认值，请确认是否正确")
        issues.append("请确认挤出机IP地址是否正确")
    
    return issues


def check_safety_limits():
    """检查安全限制"""
    print("\n" + "="*60)
    print("安全限制检查")
    print("="*60)
    
    issues = []
    
    # 检查运动安全限制
    max_velocity = globals().get('MAX_VELOCITY', 100.0)
    movement_limit = globals().get('MOVEMENT_LIMIT', 100.0)
    
    if max_velocity <= 30.0:
        print(f"✅ 最大速度: {max_velocity} mm/s (安全)")
    elif max_velocity <= 50.0:
        print(f"⚠️ 最大速度: {max_velocity} mm/s (中等)")
        issues.append("建议将最大速度降低到30 mm/s以下")
    else:
        print(f"❌ 最大速度: {max_velocity} mm/s (过高)")
        issues.append("最大速度过高，建议设置为20-30 mm/s")
    
    if movement_limit <= 50.0:
        print(f"✅ 移动限制: {movement_limit} mm (安全)")
    else:
        print(f"⚠️ 移动限制: {movement_limit} mm (较大)")
        issues.append("建议将单次移动限制设置为50mm以下")
    
    # 检查温度安全限制
    max_ext_temp = globals().get('MAX_EXTRUDER_TEMPERATURE', 250.0)
    max_bed_temp = globals().get('MAX_BED_TEMPERATURE', 100.0)
    
    if max_ext_temp <= 220.0:
        print(f"✅ 最大挤出头温度: {max_ext_temp}°C (安全)")
    else:
        print(f"⚠️ 最大挤出头温度: {max_ext_temp}°C (较高)")
        issues.append("建议将挤出头最大温度设置为220°C以下")
    
    if max_bed_temp <= 80.0:
        print(f"✅ 最大热床温度: {max_bed_temp}°C (安全)")
    else:
        print(f"⚠️ 最大热床温度: {max_bed_temp}°C (较高)")
        issues.append("建议将热床最大温度设置为80°C以下")
    
    return issues


def check_workspace_limits():
    """检查工作空间限制"""
    print("\n" + "="*60)
    print("工作空间限制检查")
    print("="*60)
    
    issues = []
    
    workspace_limits = globals().get('WORKSPACE_LIMITS', {})
    
    if workspace_limits:
        print("✅ 工作空间限制已配置:")
        for axis, limits in workspace_limits.items():
            range_size = limits['max'] - limits['min']
            print(f"  - {axis}轴: {limits['min']} 到 {limits['max']} (范围: {range_size})")
            
            if axis in ['X', 'Y', 'Z'] and range_size > 1000:
                issues.append(f"{axis}轴工作范围过大，建议限制在合理范围内")
    else:
        print("⚠️ 工作空间限制未配置")
        issues.append("建议配置工作空间限制以防止机器人超出安全范围")
    
    return issues


def check_emergency_config():
    """检查紧急配置"""
    print("\n" + "="*60)
    print("紧急配置检查")
    print("="*60)
    
    issues = []
    
    emergency_stop = globals().get('EMERGENCY_STOP_ENABLED', False)
    auto_recovery = globals().get('AUTO_ERROR_RECOVERY', True)
    max_errors = globals().get('MAX_ERROR_COUNT', 10)
    
    if emergency_stop:
        print("✅ 紧急停止检查: 已启用")
    else:
        print("⚠️ 紧急停止检查: 未启用")
        issues.append("建议启用紧急停止检查")
    
    if not auto_recovery:
        print("✅ 自动错误恢复: 已禁用 (安全)")
    else:
        print("⚠️ 自动错误恢复: 已启用")
        issues.append("建议禁用自动错误恢复，需要人工确认")
    
    if max_errors <= 5:
        print(f"✅ 最大错误次数: {max_errors} (安全)")
    else:
        print(f"⚠️ 最大错误次数: {max_errors} (较高)")
        issues.append("建议将最大错误次数设置为3-5次")
    
    return issues


def check_test_parameters():
    """检查测试参数"""
    print("\n" + "="*60)
    print("测试参数检查")
    print("="*60)
    
    issues = []
    
    test_distance = globals().get('TEST_MOVEMENT_DISTANCE', 10.0)
    test_velocity = globals().get('TEST_VELOCITY', 20.0)
    test_temperature = globals().get('TEST_TEMPERATURE', 200.0)
    
    if test_distance <= 10.0:
        print(f"✅ 测试移动距离: {test_distance} mm (安全)")
    else:
        print(f"⚠️ 测试移动距离: {test_distance} mm (较大)")
        issues.append("建议将测试移动距离设置为5-10mm")
    
    if test_velocity <= 20.0:
        print(f"✅ 测试速度: {test_velocity} mm/s (安全)")
    else:
        print(f"⚠️ 测试速度: {test_velocity} mm/s (较高)")
        issues.append("建议将测试速度设置为10-20 mm/s")
    
    if test_temperature <= 200.0:
        print(f"✅ 测试温度: {test_temperature}°C (安全)")
    else:
        print(f"⚠️ 测试温度: {test_temperature}°C (较高)")
        issues.append("建议将测试温度设置为180-200°C")
    
    return issues


def provide_recommendations(all_issues):
    """提供改进建议"""
    print("\n" + "="*60)
    print("改进建议")
    print("="*60)
    
    if not all_issues:
        print("🎉 所有配置检查通过！您的安全配置很好。")
        return
    
    print("发现以下需要改进的地方:")
    for i, issue in enumerate(all_issues, 1):
        print(f"{i}. {issue}")
    
    print("\n推荐的安全配置:")
    print("""
# 推荐的安全配置
SAFE_MODE = True
MAX_VELOCITY = 20.0
MOVEMENT_LIMIT = 50.0
MAX_EXTRUDER_TEMPERATURE = 200.0
MAX_BED_TEMPERATURE = 60.0
TEST_MOVEMENT_DISTANCE = 5.0
TEST_VELOCITY = 10.0
TEST_TEMPERATURE = 180.0
EMERGENCY_STOP_ENABLED = True
AUTO_ERROR_RECOVERY = False
MAX_ERROR_COUNT = 3
""")


def print_safety_checklist():
    """打印安全检查清单"""
    print("\n" + "🛡️"*20)
    print("硬件测试前安全检查清单")
    print("🛡️"*20)
    print("在进行硬件测试前，请确认以下各项:")
    print()
    print("□ 机器人工作空间已清空，无人员和障碍物")
    print("□ 紧急停止按钮功能正常且随手可及")
    print("□ 挤出机周围通风良好，无易燃物品")
    print("□ 准备好安全防护设备（手套、护目镜等）")
    print("□ 网络连接稳定，设备IP地址正确")
    print("□ 安全配置已检查并确认")
    print("□ 了解设备操作手册和紧急处理程序")
    print("□ 有经验的操作人员在场")
    print()
    print("⚠️ 只有在所有项目都确认后才能开始硬件测试！")
    print("🛡️"*20)


def main():
    """主函数"""
    print("🛡️ 安全配置检查工具")
    print("="*60)
    
    if not config_loaded:
        print("❌ 无法加载配置文件，请检查config.py是否存在")
        return
    
    all_issues = []
    
    # 执行各项检查
    all_issues.extend(check_basic_config())
    all_issues.extend(check_safety_limits())
    all_issues.extend(check_workspace_limits())
    all_issues.extend(check_emergency_config())
    all_issues.extend(check_test_parameters())
    
    # 提供改进建议
    provide_recommendations(all_issues)
    
    # 显示安全检查清单
    print_safety_checklist()
    
    # 显示安全验证器摘要
    if safety_validator:
        safety_validator.print_safety_summary()


if __name__ == "__main__":
    main()
