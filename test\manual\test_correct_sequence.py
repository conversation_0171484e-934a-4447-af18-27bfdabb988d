# test_correct_sequence.py
"""
测试正确的API调用顺序

根据您提供的正确调用顺序：
1. 首先调用 clear_error() 清除所有历史报警
2. 然后再尝试调用 set_servo_poweron()
3. 只有在 set_servo_poweron() 成功返回后，再调用 get_servo_state()、get_current_position() 等函数
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface进行低级别测试
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_correct_sequence():
    """
    测试正确的API调用顺序
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("正确API调用顺序测试")
        print("=" * 60)
        
        # 连接机器人
        print("连接机器人...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败，错误代码: {socket_fd}")
            return False
        
        print(f"[成功] 连接成功，套接字FD: {socket_fd}")
        print("-" * 40)
        
        # 步骤1：首先调用 clear_error() 清除所有历史报警
        print("步骤1: 清除所有历史报警")
        try:
            result = nrc.clear_error(socket_fd)
            print(f"清除错误结果: {result}")
            if result == 0:
                print("[成功] 历史报警清除成功")
            else:
                print(f"[警告] 清除历史报警失败，错误代码: {result}，但继续尝试")
        except Exception as e:
            print(f"[警告] 清除历史报警异常: {e}，但继续尝试")
        
        time.sleep(2)  # 等待错误清除生效
        print("-" * 40)
        
        # 步骤2：然后再尝试调用 set_servo_poweron()
        print("步骤2: 尝试机器人上电")
        try:
            result = nrc.set_servo_poweron(socket_fd)
            print(f"上电结果: {result}")
            if result == 0:
                print("[成功] 机器人上电成功")
                poweron_success = True
            else:
                print(f"[错误] 机器人上电失败，错误代码: {result}")
                poweron_success = False
        except Exception as e:
            print(f"[错误] 机器人上电异常: {e}")
            poweron_success = False
        
        time.sleep(3)  # 等待上电完成
        print("-" * 40)
        
        # 步骤3：只有在 set_servo_poweron() 成功返回后，再调用状态查询函数
        if poweron_success:
            print("步骤3: 上电成功后，测试状态查询函数")
            
            # 测试 get_servo_state()
            print("3.1: 测试 get_servo_state()")
            try:
                status_value = 0
                result = nrc.get_servo_state(socket_fd, status_value)
                print(f"get_servo_state 结果: {result}")
                if isinstance(result, list) and len(result) >= 2:
                    result_code = result[0]
                    servo_state = result[1]
                    if result_code == 0:
                        state_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
                        print(f"[成功] 伺服状态: {servo_state} ({state_names.get(servo_state, '未知状态')})")
                    else:
                        print(f"[警告] 获取伺服状态失败，代码: {result_code}")
                else:
                    print(f"[警告] 获取伺服状态返回格式异常: {result}")
            except Exception as e:
                print(f"[错误] get_servo_state 异常: {e}")
            
            # 测试 get_current_position()
            print("3.2: 测试 get_current_position()")
            try:
                position = [0.0] * 6
                result = nrc.get_current_position(socket_fd, 1, position)  # 1 = 直角坐标系
                print(f"get_current_position 结果: {result}")
                if result == 0:
                    print(f"[成功] 当前位置: {position}")
                else:
                    print(f"[警告] 获取位置失败，代码: {result}")
            except Exception as e:
                print(f"[错误] get_current_position 异常: {e}")
            
            # 测试 get_robot_running_state()
            print("3.3: 测试 get_robot_running_state()")
            try:
                status_value = 0
                result = nrc.get_robot_running_state(socket_fd, status_value)
                print(f"get_robot_running_state 结果: {result}")
                if isinstance(result, list) and len(result) >= 2:
                    result_code = result[0]
                    running_state = result[1]
                    if result_code == 0:
                        state_names = {0: "停止状态", 1: "运行状态", 2: "暂停状态", 3: "错误状态", 4: "急停状态", 5: "准备状态"}
                        print(f"[成功] 运行状态: {running_state} ({state_names.get(running_state, '未知状态')})")
                    else:
                        print(f"[警告] 获取运行状态失败，代码: {result_code}")
                else:
                    print(f"[警告] 获取运行状态返回格式异常: {result}")
            except Exception as e:
                print(f"[错误] get_robot_running_state 异常: {e}")
            
            # 测试 get_current_mode()
            print("3.4: 测试 get_current_mode()")
            try:
                mode_value = 0
                result = nrc.get_current_mode(socket_fd, mode_value)
                print(f"get_current_mode 结果: {result}")
                if isinstance(result, list) and len(result) >= 2:
                    result_code = result[0]
                    current_mode = result[1]
                    if result_code == 0:
                        mode_names = {0: "示教模式", 1: "运行模式", 2: "远程模式"}
                        print(f"[成功] 当前模式: {current_mode} ({mode_names.get(current_mode, '未知模式')})")
                    else:
                        print(f"[警告] 获取模式失败，代码: {result_code}")
                else:
                    print(f"[警告] 获取模式返回格式异常: {result}")
            except Exception as e:
                print(f"[错误] get_current_mode 异常: {e}")
            
            # 测试 get_speed()
            print("3.5: 测试 get_speed()")
            try:
                speed_value = 0
                result = nrc.get_speed(socket_fd, speed_value)
                print(f"get_speed 结果: {result}")
                if isinstance(result, list) and len(result) >= 2:
                    result_code = result[0]
                    current_speed = result[1]
                    if result_code == 0:
                        print(f"[成功] 当前速度: {current_speed}%")
                    else:
                        print(f"[警告] 获取速度失败，代码: {result_code}")
                else:
                    print(f"[警告] 获取速度返回格式异常: {result}")
            except Exception as e:
                print(f"[错误] get_speed 异常: {e}")
            
            # 测试一些设置函数
            print("3.6: 测试设置函数")
            try:
                # 设置远程模式
                result = nrc.set_current_mode(socket_fd, 2)
                print(f"set_current_mode(远程模式) 结果: {result}")
                if result == 0:
                    print("[成功] 远程模式设置成功")
                
                # 设置速度
                result = nrc.set_speed(socket_fd, 30)
                print(f"set_speed(30%) 结果: {result}")
                if result == 0:
                    print("[成功] 速度设置成功")
                
            except Exception as e:
                print(f"[错误] 设置函数异常: {e}")
            
        else:
            print("步骤3: 由于上电失败，跳过状态查询测试")
        
        print("-" * 40)
        print("[成功] 正确API调用顺序测试完成！")
        
        # 总结
        print("\n总结:")
        print("1. clear_error() - 清除历史报警")
        print("2. set_servo_poweron() - 机器人上电")
        if poweron_success:
            print("3. 上电成功后，状态查询函数应该能正常工作")
        else:
            print("3. 上电失败，需要检查机器人状态")
        
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            print("-" * 40)
            print("正在安全关闭...")
            
            try:
                # 如果上电成功，先下电
                if 'poweron_success' in locals() and poweron_success:
                    print("机器人下电...")
                    nrc.set_servo_poweroff(socket_fd)
                    time.sleep(1)
                
                # 断开连接
                print("断开连接...")
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except Exception as e:
                print(f"[警告] 关闭过程中发生异常: {e}")


def main():
    """
    主函数
    """
    print("INEXBOT 正确API调用顺序测试")
    print("测试正确的调用顺序：clear_error() → set_servo_poweron() → 状态查询函数")
    print()
    
    user_input = input("确认开始测试，输入 'yes' 继续: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return
    
    if test_correct_sequence():
        print("\n[成功] 正确API调用顺序测试完成！")
    else:
        print("\n[失败] 正确API调用顺序测试失败！")


if __name__ == "__main__":
    main()
