# config.py
"""
机器人3D打印控制系统配置文件

该文件包含所有硬件设备的连接配置参数，
使得我们可以轻松修改配置而无需更改核心代码。
"""

# INEXBOT 机械臂的配置
# TODO: 请用户将 "************" 替换为实际的机器人IP地址
ROBOT_IP = "************"
# TODO: 请用户确认并替换为实际的机器人通信端口
ROBOT_PORT = 6001

# Klipper 挤出机的配置 (后续步骤会用到)
# TODO: 请用户将 "************" 替换为实际运行Klipper的设备IP地址
KLIPPER_IP = "************"
# TODO: 请用户确认并替换为实际的Klipper通信端口
KLIPPER_PORT = 7125

# 系统配置
DEBUG_MODE = True  # 调试模式，启用详细日志输出
CONNECTION_TIMEOUT = 5  # 连接超时时间（秒）
RETRY_ATTEMPTS = 3  # 连接重试次数

# 安全配置 - 硬件测试安全限制
SAFE_MODE = True  # 安全模式，启用各种安全限制

# 机器人运动安全限制
MAX_VELOCITY = 20.0  # 最大运动速度 (mm/s) - 正常速度的1/3，确保安全
MIN_VELOCITY = 1.0   # 最小运动速度 (mm/s)
MAX_ACCELERATION = 50.0  # 最大加速度 (mm/s²)
MOVEMENT_LIMIT = 50.0  # 单次移动距离限制 (mm)
SAFE_BLENDING_RADIUS = 1.0  # 安全的路径平滑半径 (mm)

# 机器人位置安全限制 (工作空间边界)
WORKSPACE_LIMITS = {
    'X': {'min': -500, 'max': 500},  # X轴工作范围 (mm)
    'Y': {'min': -500, 'max': 500},  # Y轴工作范围 (mm)
    'Z': {'min': 0, 'max': 800},     # Z轴工作范围 (mm)
    'A': {'min': -180, 'max': 180},  # A轴旋转范围 (度)
    'B': {'min': -180, 'max': 180},  # B轴旋转范围 (度)
    'C': {'min': -180, 'max': 180}   # C轴旋转范围 (度)
}

# 挤出机温度安全限制
MAX_EXTRUDER_TEMPERATURE = 200.0  # 挤出头最大温度 (°C) - PLA安全温度
MAX_BED_TEMPERATURE = 60.0        # 热床最大温度 (°C)
MIN_TEMPERATURE = 15.0            # 最低环境温度 (°C)
TEMPERATURE_CHANGE_RATE = 5.0     # 温度变化速率限制 (°C/s)
TEMPERATURE_TIMEOUT = 300         # 温度等待超时时间 (秒)

# 挤出安全限制
MAX_EXTRUDE_AMOUNT = 10.0         # 单次最大挤出量 (mm)
MAX_EXTRUDE_SPEED = 300           # 最大挤出速度 (mm/min)
MIN_EXTRUDE_SPEED = 60            # 最小挤出速度 (mm/min)

# 测试安全配置
TEST_MOVEMENT_DISTANCE = 5.0      # 测试时的安全移动距离 (mm)
TEST_VELOCITY = 10.0              # 测试时的安全速度 (mm/s)
TEST_TEMPERATURE = 180.0          # 测试时的安全温度 (°C)

# 紧急停止和错误处理
EMERGENCY_STOP_ENABLED = True     # 启用紧急停止检查
AUTO_ERROR_RECOVERY = False       # 禁用自动错误恢复，需要人工确认
MAX_ERROR_COUNT = 3               # 最大连续错误次数
ERROR_COOLDOWN_TIME = 10          # 错误后的冷却时间 (秒)

# 监控和日志配置
ENABLE_SAFETY_MONITORING = True   # 启用安全监控
LOG_SAFETY_EVENTS = True          # 记录安全事件
SAFETY_LOG_FILE = "safety.log"   # 安全日志文件名
