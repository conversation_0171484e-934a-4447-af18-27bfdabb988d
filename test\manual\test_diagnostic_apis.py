# test_diagnostic_apis.py
"""
测试诊断相关的API调用，尝试找出为什么基本API调用失败

重点测试：
1. 哪些API可以成功调用
2. 是否有错误信息或状态信息可以获取
3. 尝试不同的API调用组合
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_all_working_apis():
    """
    测试所有可能工作的API
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("诊断所有可用的API")
        print("=" * 60)
        
        # 连接
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败")
            return False
        
        print(f"[成功] 连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        # 测试所有可能成功的API
        successful_apis = []
        failed_apis = []
        
        # 1. clear_error
        print("1. 测试 clear_error")
        try:
            result = nrc.clear_error(socket_fd)
            print(f"   clear_error: {result}")
            if result == 0:
                successful_apis.append("clear_error")
            else:
                failed_apis.append(f"clear_error({result})")
        except Exception as e:
            failed_apis.append(f"clear_error(异常: {e})")
        
        # 2. set_current_mode
        print("2. 测试 set_current_mode")
        for mode in [0, 1, 2]:  # 示教、运行、远程
            try:
                result = nrc.set_current_mode(socket_fd, mode)
                print(f"   set_current_mode({mode}): {result}")
                if result == 0:
                    successful_apis.append(f"set_current_mode({mode})")
                else:
                    failed_apis.append(f"set_current_mode({mode})={result}")
            except Exception as e:
                failed_apis.append(f"set_current_mode({mode})(异常: {e})")
        
        # 3. get_current_mode
        print("3. 测试 get_current_mode")
        try:
            mode_value = 0
            result = nrc.get_current_mode(socket_fd, mode_value)
            print(f"   get_current_mode: {result}")
            if isinstance(result, list) and result[0] == 0:
                successful_apis.append("get_current_mode")
            else:
                failed_apis.append(f"get_current_mode({result})")
        except Exception as e:
            failed_apis.append(f"get_current_mode(异常: {e})")
        
        # 4. set_speed
        print("4. 测试 set_speed")
        try:
            result = nrc.set_speed(socket_fd, 50)
            print(f"   set_speed(50): {result}")
            if result == 0:
                successful_apis.append("set_speed")
            else:
                failed_apis.append(f"set_speed({result})")
        except Exception as e:
            failed_apis.append(f"set_speed(异常: {e})")
        
        # 5. get_speed
        print("5. 测试 get_speed")
        try:
            speed_value = 0
            result = nrc.get_speed(socket_fd, speed_value)
            print(f"   get_speed: {result}")
            if isinstance(result, list) and result[0] == 0:
                successful_apis.append("get_speed")
            else:
                failed_apis.append(f"get_speed({result})")
        except Exception as e:
            failed_apis.append(f"get_speed(异常: {e})")
        
        # 6. 尝试一些可能的诊断API
        print("6. 测试可能的诊断API")
        
        # 尝试获取机器人信息
        diagnostic_apis = [
            "get_robot_type",
            "get_servo_state", 
            "get_robot_running_state",
            "get_current_position"
        ]
        
        for api_name in diagnostic_apis:
            print(f"   测试 {api_name}")
            try:
                if api_name == "get_robot_type":
                    # 这个API可能需要特殊的参数类型
                    pass  # 暂时跳过
                elif api_name == "get_servo_state":
                    status_value = 0
                    result = getattr(nrc, api_name)(socket_fd, status_value)
                elif api_name == "get_robot_running_state":
                    status_value = 0
                    result = getattr(nrc, api_name)(socket_fd, status_value)
                elif api_name == "get_current_position":
                    # 这个需要特殊的向量参数
                    pass  # 暂时跳过
                
                print(f"     {api_name}: {result}")
                if isinstance(result, list) and result[0] == 0:
                    successful_apis.append(api_name)
                else:
                    failed_apis.append(f"{api_name}({result})")
            except Exception as e:
                failed_apis.append(f"{api_name}(异常: {str(e)[:50]}...)")
        
        # 7. 尝试一些设置API
        print("7. 测试设置API")
        setting_apis = [
            ("set_servo_state", [0]),  # 停止状态
            ("set_servo_state", [1]),  # 就绪状态
        ]
        
        for api_name, params in setting_apis:
            print(f"   测试 {api_name}{params}")
            try:
                result = getattr(nrc, api_name)(socket_fd, *params)
                print(f"     {api_name}{params}: {result}")
                if result == 0:
                    successful_apis.append(f"{api_name}{params}")
                else:
                    failed_apis.append(f"{api_name}{params}={result}")
            except Exception as e:
                failed_apis.append(f"{api_name}{params}(异常: {str(e)[:50]}...)")
        
        # 总结
        print("\n" + "=" * 60)
        print("API测试总结")
        print("=" * 60)
        
        print(f"\n成功的API ({len(successful_apis)}):")
        for api in successful_apis:
            print(f"  ✅ {api}")
        
        print(f"\n失败的API ({len(failed_apis)}):")
        for api in failed_apis:
            print(f"  ❌ {api}")
        
        # 分析
        print("\n分析:")
        if len(successful_apis) > 0:
            print("- 连接正常，部分API可以工作")
            print("- 问题可能是机器人状态或特定API的调用条件")
        else:
            print("- 只有基本的设置API工作，状态查询API都失败")
            print("- 可能是机器人处于特殊状态或需要特定的初始化")
        
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                nrc.disconnect_robot(socket_fd)
                print("\n[成功] 连接已断开")
            except:
                pass


def test_error_recovery():
    """
    测试错误恢复流程
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("错误恢复流程测试")
        print("=" * 60)
        
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            return False
        
        print(f"连接成功，FD: {socket_fd}")
        time.sleep(2)
        
        # 尝试多次清除错误
        print("1. 多次清除错误")
        for i in range(3):
            result = nrc.clear_error(socket_fd)
            print(f"   第{i+1}次 clear_error: {result}")
            time.sleep(1)
        
        # 设置远程模式
        print("2. 设置远程模式")
        result = nrc.set_current_mode(socket_fd, 2)
        print(f"   set_current_mode(2): {result}")
        time.sleep(1)
        
        # 尝试获取状态
        print("3. 尝试获取状态")
        status_value = 0
        result = nrc.get_servo_state(socket_fd, status_value)
        print(f"   get_servo_state: {result}")
        
        if isinstance(result, list) and result[0] != 0:
            print(f"   状态查询失败，错误代码: {result[0]}")
            print("   这可能表明机器人处于特殊状态")
        
        return True
        
    except Exception as e:
        print(f"错误恢复测试中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            try:
                nrc.disconnect_robot(socket_fd)
            except:
                pass


def main():
    """
    主函数
    """
    print("INEXBOT API诊断测试")
    print()
    print("这个测试用于诊断哪些API可以工作，哪些不能工作")
    print()
    
    print("1. 运行所有API测试")
    print("2. 运行错误恢复测试")
    print("3. 两个测试都运行")
    
    choice = input("\n请选择测试 (1/2/3): ").strip()
    
    if choice == "1":
        test_all_working_apis()
    elif choice == "2":
        test_error_recovery()
    elif choice == "3":
        test_all_working_apis()
        print("\n" + "="*60)
        test_error_recovery()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
