# test_working_apis.py
"""
测试可用的API功能

专门测试那些返回成功的API，看看我们能实现什么功能
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接导入nrc_interface进行低级别测试
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
    print("[成功] nrc_interface 导入成功")
except ImportError as e:
    print(f"[错误] nrc_interface 导入失败: {e}")
    sys.exit(1)

from config import ROBOT_IP, ROBOT_PORT


def test_working_apis():
    """
    测试可用的API功能
    """
    socket_fd = None
    
    try:
        print("=" * 60)
        print("可用API功能测试")
        print("=" * 60)
        
        # 连接机器人
        print("连接机器人...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd < 0:
            print(f"[错误] 连接失败，错误代码: {socket_fd}")
            return False
        
        print(f"[成功] 连接成功，套接字FD: {socket_fd}")
        print("-" * 40)
        
        # 测试库版本
        print("测试1: 获取库版本")
        try:
            version = nrc.get_library_version()
            print(f"[成功] 库版本: {version}")
        except Exception as e:
            print(f"[错误] 获取库版本失败: {e}")
        
        print("-" * 40)
        
        # 测试连接状态
        print("测试2: 获取连接状态")
        try:
            conn_status = nrc.get_connection_status(socket_fd)
            print(f"[成功] 连接状态: {conn_status}")
        except Exception as e:
            print(f"[错误] 获取连接状态失败: {e}")
        
        print("-" * 40)
        
        # 测试清除错误（已知成功）
        print("测试3: 清除错误")
        try:
            result = nrc.clear_error(socket_fd)
            print(f"清除错误结果: {result}")
            if result == 0:
                print("[成功] 错误清除成功")
            else:
                print(f"[警告] 清除错误失败，代码: {result}")
        except Exception as e:
            print(f"[错误] 清除错误失败: {e}")
        
        print("-" * 40)
        
        # 测试速度设置（已知成功）
        print("测试4: 速度控制")
        try:
            # 设置不同的速度值
            speeds = [10, 25, 50, 75, 100]
            for speed in speeds:
                result = nrc.set_speed(socket_fd, speed)
                print(f"设置速度{speed}%结果: {result}")
                if result == 0:
                    print(f"[成功] 速度设置为{speed}%成功")
                else:
                    print(f"[警告] 设置速度{speed}%失败，代码: {result}")
                time.sleep(0.5)
        except Exception as e:
            print(f"[错误] 速度控制失败: {e}")
        
        print("-" * 40)
        
        # 测试模式设置
        print("测试5: 模式设置")
        try:
            modes = [0, 1, 2]  # 示教模式, 运行模式, 远程模式
            mode_names = ["示教模式", "运行模式", "远程模式"]
            
            for i, mode in enumerate(modes):
                result = nrc.set_current_mode(socket_fd, mode)
                print(f"设置{mode_names[i]}结果: {result}")
                if result == 0:
                    print(f"[成功] {mode_names[i]}设置成功")
                else:
                    print(f"[警告] {mode_names[i]}设置失败，代码: {result}")
                time.sleep(1)
        except Exception as e:
            print(f"[错误] 模式设置失败: {e}")
        
        print("-" * 40)
        
        # 测试坐标系设置
        print("测试6: 坐标系设置")
        try:
            coords = [0, 1, 2, 3]  # 关节, 直角, 工具, 用户
            coord_names = ["关节坐标系", "直角坐标系", "工具坐标系", "用户坐标系"]
            
            for i, coord in enumerate(coords):
                result = nrc.set_current_coord(socket_fd, coord)
                print(f"设置{coord_names[i]}结果: {result}")
                if result == 0:
                    print(f"[成功] {coord_names[i]}设置成功")
                else:
                    print(f"[警告] {coord_names[i]}设置失败，代码: {result}")
                time.sleep(0.5)
        except Exception as e:
            print(f"[错误] 坐标系设置失败: {e}")
        
        print("-" * 40)
        
        # 测试工具手设置
        print("测试7: 工具手设置")
        try:
            tool_numbers = [0, 1, 2]
            
            for tool_num in tool_numbers:
                result = nrc.set_tool_hand_number(socket_fd, tool_num)
                print(f"设置工具手{tool_num}结果: {result}")
                if result == 0:
                    print(f"[成功] 工具手{tool_num}设置成功")
                else:
                    print(f"[警告] 工具手{tool_num}设置失败，代码: {result}")
                time.sleep(0.5)
        except Exception as e:
            print(f"[错误] 工具手设置失败: {e}")
        
        print("-" * 40)
        
        # 测试用户坐标设置
        print("测试8: 用户坐标设置")
        try:
            user_coords = [0, 1, 2]
            
            for user_coord in user_coords:
                result = nrc.set_user_coord_number(socket_fd, user_coord)
                print(f"设置用户坐标{user_coord}结果: {result}")
                if result == 0:
                    print(f"[成功] 用户坐标{user_coord}设置成功")
                else:
                    print(f"[警告] 用户坐标{user_coord}设置失败，代码: {result}")
                time.sleep(0.5)
        except Exception as e:
            print(f"[错误] 用户坐标设置失败: {e}")
        
        print("-" * 40)
        
        # 测试示教类型设置
        print("测试9: 示教类型设置")
        try:
            teach_types = [0, 1]  # 点动, 拖拽
            teach_names = ["点动", "拖拽"]
            
            for i, teach_type in enumerate(teach_types):
                result = nrc.set_teach_type(socket_fd, teach_type)
                print(f"设置示教类型{teach_names[i]}结果: {result}")
                if result == 0:
                    print(f"[成功] 示教类型{teach_names[i]}设置成功")
                else:
                    print(f"[警告] 示教类型{teach_names[i]}设置失败，代码: {result}")
                time.sleep(0.5)
        except Exception as e:
            print(f"[错误] 示教类型设置失败: {e}")
        
        print("-" * 40)
        
        # 尝试一些运动命令（即使可能失败）
        print("测试10: 尝试运动命令")
        try:
            # 尝试停止运动
            result = nrc.robot_stop(socket_fd)
            print(f"停止运动结果: {result}")
            if result == 0:
                print("[成功] 停止运动命令成功")
            else:
                print(f"[警告] 停止运动失败，代码: {result}")
            
            # 尝试暂停
            result = nrc.robot_pause(socket_fd)
            print(f"暂停运动结果: {result}")
            if result == 0:
                print("[成功] 暂停运动命令成功")
            else:
                print(f"[警告] 暂停运动失败，代码: {result}")
            
            # 尝试继续
            result = nrc.robot_continue(socket_fd)
            print(f"继续运动结果: {result}")
            if result == 0:
                print("[成功] 继续运动命令成功")
            else:
                print(f"[警告] 继续运动失败，代码: {result}")
                
        except Exception as e:
            print(f"[错误] 运动命令失败: {e}")
        
        print("-" * 40)
        print("[成功] 可用API功能测试完成！")
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
        
    finally:
        if socket_fd is not None:
            print("-" * 40)
            print("断开连接...")
            try:
                nrc.disconnect_robot(socket_fd)
                print("[成功] 连接已断开")
            except Exception as e:
                print(f"[警告] 断开连接时发生异常: {e}")


def main():
    """
    主函数
    """
    print("机器人可用API功能测试")
    print("该测试将专门测试那些已知可用的API功能")
    print()
    
    user_input = input("确认开始测试，输入 'yes' 继续: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return
    
    if test_working_apis():
        print("\n[成功] 可用API功能测试完成！")
    else:
        print("\n[失败] 可用API功能测试失败！")


if __name__ == "__main__":
    main()
