# src/core/validators.py
"""
参数验证模块

该模块提供各种参数验证功能，确保传入的参数符合INEXBOT机器人的要求。

作者: Augment Agent
日期: 2025-07-15
"""


class ParameterValidator:
    """
    参数验证器
    
    提供各种参数验证方法，确保参数的正确性和安全性。
    """
    
    @staticmethod
    def validate_position(position, position_type="笛卡尔坐标"):
        """
        验证位置参数
        
        Args:
            position (list): 位置参数列表
            position_type (str): 位置类型描述
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(position, (list, tuple)):
            raise ValueError(f"{position_type}必须是列表或元组类型")
        
        if len(position) != 6:
            raise ValueError(f"{position_type}必须包含6个坐标值 (X,Y,Z,A,B,C)")
        
        for i, coord in enumerate(position):
            if not isinstance(coord, (int, float)):
                raise ValueError(f"{position_type}第{i+1}个坐标值必须是数字类型")
        
        return True
    
    @staticmethod
    def validate_velocity(velocity):
        """
        验证速度参数
        
        Args:
            velocity (float): 速度值
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(velocity, (int, float)):
            raise ValueError("速度必须是数字类型")
        
        if velocity <= 0:
            raise ValueError("速度必须大于0")
        
        if velocity > 1000:  # 假设最大速度限制
            raise ValueError("速度不能超过1000 mm/s")
        
        return True
    
    @staticmethod
    def validate_blending_radius(blending_radius):
        """
        验证路径平滑半径参数
        
        Args:
            blending_radius (float): 平滑半径值
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(blending_radius, (int, float)):
            raise ValueError("路径平滑半径必须是数字类型")
        
        if blending_radius < 0:
            raise ValueError("路径平滑半径不能为负数")
        
        return True
    
    @staticmethod
    def validate_speed_percentage(speed_percentage):
        """
        验证速度比例参数
        
        Args:
            speed_percentage (int): 速度比例 (1-100)
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(speed_percentage, (int, float)):
            raise ValueError("速度比例必须是数字类型")
        
        # 根据官方文档，速度参数应为整数，范围1-100
        speed_int = int(round(speed_percentage))
        if not (1 <= speed_int <= 100):
            raise ValueError("速度比例必须在1-100%范围内")
        
        return True
    
    @staticmethod
    def validate_coordinate_type(coord_type):
        """
        验证坐标系类型参数
        
        Args:
            coord_type (int): 坐标系类型
                0: 关节坐标系
                1: 直角坐标系
                2: 工具坐标系
                3: 用户坐标系
                
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(coord_type, int):
            raise ValueError("坐标系类型必须是整数")
        
        if coord_type not in [0, 1, 2, 3]:
            raise ValueError("坐标系类型必须是0(关节)、1(直角)、2(工具)或3(用户)")
        
        return True
    
    @staticmethod
    def validate_servo_state(state):
        """
        验证伺服状态参数
        
        Args:
            state (int): 伺服状态
                0: 停止状态
                1: 就绪状态
                
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(state, int):
            raise ValueError("伺服状态必须是整数")
        
        if state not in [0, 1]:
            raise ValueError("伺服状态必须是0(停止)或1(就绪)")
        
        return True
    
    @staticmethod
    def validate_ip_address(ip):
        """
        验证IP地址格式
        
        Args:
            ip (str): IP地址字符串
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(ip, str):
            raise ValueError("IP地址必须是字符串类型")
        
        parts = ip.split('.')
        if len(parts) != 4:
            raise ValueError("IP地址格式不正确")
        
        for part in parts:
            try:
                num = int(part)
                if not (0 <= num <= 255):
                    raise ValueError("IP地址每个部分必须在0-255范围内")
            except ValueError:
                raise ValueError("IP地址包含非数字字符")
        
        return True
    
    @staticmethod
    def validate_port(port):
        """
        验证端口号
        
        Args:
            port (int): 端口号
            
        Returns:
            bool: 验证通过返回True
            
        Raises:
            ValueError: 参数不符合要求时抛出异常
        """
        if not isinstance(port, int):
            raise ValueError("端口号必须是整数类型")
        
        if not (1 <= port <= 65535):
            raise ValueError("端口号必须在1-65535范围内")
        
        return True
    
    @staticmethod
    def safe_validate(validator_func, *args, **kwargs):
        """
        安全验证，捕获异常并返回布尔值
        
        Args:
            validator_func: 验证函数
            *args: 验证函数参数
            **kwargs: 验证函数关键字参数
            
        Returns:
            tuple: (是否通过验证, 错误消息)
        """
        try:
            validator_func(*args, **kwargs)
            return True, None
        except ValueError as e:
            return False, str(e)
        except Exception as e:
            return False, f"验证异常: {e}"
