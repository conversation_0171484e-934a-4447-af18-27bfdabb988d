# 测试目录说明

本目录包含机器人3D打印控制系统的所有测试文件和测试相关文档。

## 📁 目录结构

```
test/
├── README.md                    # 本文件 - 测试目录说明
├── TESTING_GUIDE.md            # 完整测试指南文档
├── TEST_INDEX.md               # 测试文件索引和快速查找
├── __init__.py                 # Python包初始化文件
├── unit/                       # 单元测试
│   ├── __init__.py
│   ├── test_motion.py          # 运动控制模块测试
│   └── test_servo.py           # 伺服控制模块测试
├── integration/                # 集成测试
│   ├── __init__.py
│   ├── test_robot_complete.py  # 机器人完整功能测试
│   └── test_extruder_complete.py # 挤出机完整功能测试
├── manual/                     # 手动测试（需要硬件）
│   ├── __init__.py
│   ├── test_hardware_connection.py # 硬件连接测试
│   └── test_full_workflow.py   # 完整工作流程测试
└── fixtures/                   # 测试工具和辅助功能
    ├── __init__.py
    └── test_helpers.py          # 测试辅助工具
```

## 🧪 测试类型说明

### 单元测试 (unit/)
- **目的**：测试单个模块的功能
- **特点**：快速、独立、不依赖外部硬件
- **运行方式**：`pytest test/unit/`
- **适用场景**：开发过程中的快速验证

### 集成测试 (integration/)
- **目的**：测试多个模块之间的协作
- **特点**：验证系统集成，可在开发模式下运行
- **运行方式**：`pytest test/integration/`
- **适用场景**：功能完成后的集成验证

### 手动测试 (manual/)
- **目的**：测试实际硬件交互
- **特点**：需要真实硬件连接，人工观察结果
- **运行方式**：直接执行Python脚本
- **适用场景**：最终验收和硬件兼容性测试

### 测试工具 (fixtures/)
- **目的**：提供测试辅助功能
- **特点**：共享的测试资源和工具函数
- **使用方式**：被其他测试文件导入使用
- **内容**：模拟对象、测试数据、验证工具

## 🚀 快速开始

### 1. 运行开发环境测试（推荐）

```bash
# 运行所有单元测试
python scripts/run_tests.py unit

# 运行所有集成测试
python scripts/run_tests.py integration

# 运行所有开发环境测试
python scripts/run_tests.py dev
```

### 2. 运行特定测试

```bash
# 运行特定测试文件
pytest test/unit/test_motion.py

# 运行特定测试类
pytest test/unit/test_motion.py::TestMotionController

# 运行特定测试方法
pytest test/unit/test_motion.py::TestMotionController::test_move_linear_valid_params
```

### 3. 运行硬件测试（需要硬件连接）

```bash
# 测试硬件连接
python test/manual/test_hardware_connection.py

# 测试完整工作流程
python test/manual/test_full_workflow.py
```

## ⚠️ 重要提示

### 安全注意事项
- 运行手动测试前，确保机器人工作空间安全
- 准备紧急停止按钮
- 确认所有人员远离机器人工作区域

### 配置要求
- 更新`config.py`中的设备IP地址
- 确保网络连接正常
- 验证INEXBOT SDK文件存在

### 依赖要求
```bash
pip install pytest pytest-mock requests
```

## 📖 详细文档

如需了解详细的测试指南和索引，请参考：

- **[📖 完整测试指南](TESTING_GUIDE.md)** - 详细的测试环境准备、执行方法和故障排除
- **[📋 测试索引](TEST_INDEX.md)** - 所有测试文件的详细索引和快速查找

## 🔧 故障排除

### 常见问题

1. **导入错误**
   - 确保Python路径正确设置
   - 检查`__init__.py`文件存在
   - 验证依赖包已安装

2. **连接失败**
   - 检查IP地址配置
   - 确认网络连通性
   - 验证设备状态

3. **测试失败**
   - 查看详细错误信息
   - 检查测试环境配置
   - 参考测试指南进行排查

### 获取帮助

- 查看[完整测试指南](TESTING_GUIDE.md)
- 查看项目[README](../README.md)
- 联系项目维护者

---

*本目录的测试文件会随着项目发展持续更新和完善。*
