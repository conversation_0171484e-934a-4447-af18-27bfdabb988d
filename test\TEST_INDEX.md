# 测试索引

本文档提供了所有测试文件的快速索引，帮助您快速找到需要的测试。

## 📋 测试文件索引

### 单元测试 (unit/)

| 文件名 | 测试内容 | 主要测试类 | 硬件要求 |
|--------|----------|------------|----------|
| `test_motion.py` | 运动控制模块 | `TestMotionController` | 不需要 |
| `test_servo.py` | 伺服控制模块 | `TestServoController` | 不需要 |

#### test_motion.py 详细测试用例
- `test_move_linear_valid_params` - 测试有效参数的线性运动
- `test_move_linear_invalid_position` - 测试无效位置参数
- `test_move_linear_invalid_velocity` - 测试无效速度参数
- `test_move_linear_invalid_blending` - 测试无效混合半径参数
- `test_robot_movej_valid_params` - 测试有效参数的关节运动
- `test_robot_movej_invalid_params` - 测试无效关节运动参数
- `test_development_mode_simulation` - 测试开发模式模拟

#### test_servo.py 详细测试用例
- `test_enable_servos_success` - 测试伺服使能成功
- `test_enable_servos_connection_failed` - 测试连接失败时的伺服使能
- `test_disable_servos_success` - 测试伺服关闭成功
- `test_get_servo_state_success` - 测试获取伺服状态
- `test_clear_servo_error_success` - 测试清除伺服错误
- `test_set_servo_poweron_success` - 测试伺服上电
- `test_set_servo_poweroff_success` - 测试伺服下电

### 集成测试 (integration/)

| 文件名 | 测试内容 | 主要测试类 | 硬件要求 |
|--------|----------|------------|----------|
| `test_robot_complete.py` | 机器人完整功能 | `RobotEnhancedTester` | 可选 |
| `test_extruder_complete.py` | 挤出机完整功能 | `ExtruderTester` | 可选 |

#### test_robot_complete.py 详细测试阶段
- **阶段1：连接测试**
  - 机器人连接验证
  - 连接状态查询
  - 基础通信测试
- **阶段2：伺服和状态测试**
  - 伺服使能/关闭测试
  - 状态监控测试
  - 错误处理测试
- **阶段3：运动控制测试**
  - 位置读取测试
  - 线性运动测试
  - 关节运动测试
- **阶段4：高级功能测试**
  - 速度控制测试
  - 错误恢复测试
  - 状态监控测试

#### test_extruder_complete.py 详细测试阶段
- **连接测试**：验证与Klipper的连接
- **温度控制测试**：温度设置和监控
- **挤出功能测试**：挤出操作和控制
- **G-code测试**：G-code指令处理

### 手动测试 (manual/)

| 文件名 | 测试内容 | 测试类型 | 硬件要求 |
|--------|----------|----------|----------|
| `test_hardware_connection.py` | 硬件连接测试 | 交互式 | 需要 |
| `test_full_workflow.py` | 完整工作流程 | 交互式 | 需要 |

#### test_hardware_connection.py 测试流程
1. 机器人连接测试
2. 伺服状态检查和使能
3. 位置读取验证
4. 安全运动测试
5. 伺服关闭和断开连接

#### test_full_workflow.py 测试流程
1. 系统初始化
2. 机器人和挤出机连接
3. 温度预热
4. 协调运动测试
5. 完整打印流程模拟
6. 系统关闭

### 测试工具 (fixtures/)

| 文件名 | 功能描述 | 主要内容 |
|--------|----------|----------|
| `test_helpers.py` | 测试辅助工具 | 验证函数、模拟对象、测试数据 |

#### test_helpers.py 主要功能
- `check_file_exists()` - 检查文件是否存在
- `check_import()` - 检查模块导入
- `verify_robot_interface()` - 验证机器人接口
- `verify_extruder_interface()` - 验证挤出机接口
- `run_basic_tests()` - 运行基础测试
- `run_import_tests()` - 运行导入测试

## 🎯 测试选择指南

### 根据开发阶段选择测试

#### 开发阶段
```bash
# 快速验证代码更改
pytest test/unit/

# 验证模块集成
pytest test/integration/ --dev-mode
```

#### 测试阶段
```bash
# 完整功能验证
python test/integration/test_robot_complete.py
python test/integration/test_extruder_complete.py
```

#### 部署前验证
```bash
# 硬件兼容性测试
python test/manual/test_hardware_connection.py
python test/manual/test_full_workflow.py
```

### 根据测试目的选择

#### 功能验证
- 单元测试：验证单个功能模块
- 集成测试：验证模块间协作
- 手动测试：验证实际硬件交互

#### 性能测试
- 响应时间测试：`test_motion.py` 中的性能测试用例
- 并发测试：集成测试中的并发操作验证
- 稳定性测试：手动测试中的长时间运行测试

#### 安全测试
- 参数验证：单元测试中的边界条件测试
- 错误处理：所有测试类型中的异常处理验证
- 硬件安全：手动测试中的安全操作验证

## 🔍 测试执行命令速查

### pytest命令
```bash
# 运行所有单元测试
pytest test/unit/

# 运行特定测试文件
pytest test/unit/test_motion.py

# 运行特定测试类
pytest test/unit/test_motion.py::TestMotionController

# 运行特定测试方法
pytest test/unit/test_motion.py::TestMotionController::test_move_linear_valid_params

# 显示详细输出
pytest test/unit/ -v

# 显示测试覆盖率
pytest test/unit/ --cov=src

# 只运行失败的测试
pytest test/unit/ --lf
```

### 直接执行命令
```bash
# 手动测试
python test/manual/test_hardware_connection.py
python test/manual/test_full_workflow.py

# 集成测试
python test/integration/test_robot_complete.py
python test/integration/test_extruder_complete.py

# 测试工具
python test/fixtures/test_helpers.py
```

### 脚本执行命令
```bash
# 使用项目脚本
python scripts/run_tests.py unit
python scripts/run_tests.py integration
python scripts/run_tests.py manual
python scripts/run_tests.py dev
```

## 📊 测试结果解读

### 单元测试结果
- ✅ PASSED：测试通过
- ❌ FAILED：测试失败，需要检查代码
- ⚠️ SKIPPED：测试跳过，通常是条件不满足
- 🔄 XFAIL：预期失败，已知问题

### 集成测试结果
- 连接状态：显示硬件连接是否成功
- 功能状态：显示各功能模块是否正常
- 性能指标：显示响应时间和处理速度
- 错误信息：显示详细的错误原因

### 手动测试结果
- 需要人工观察和确认
- 关注机器人运动是否平滑
- 检查温度控制是否准确
- 验证安全机制是否有效

---

## 📞 获取帮助

如果您在使用测试时遇到问题：

1. 查看[完整测试指南](TESTING_GUIDE.md)
2. 查看[测试目录说明](README.md)
3. 查看项目[主README](../README.md)
4. 联系项目维护者

---

*本索引会随着测试文件的更新而持续维护。*
