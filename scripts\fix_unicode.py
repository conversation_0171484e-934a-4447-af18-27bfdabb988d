#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Unicode字符编码问题

将代码中的Unicode符号替换为ASCII兼容的文本，解决Windows GBK编码问题。
"""

import os
import re
from pathlib import Path

# Unicode字符映射
UNICODE_REPLACEMENTS = {
    '✓': '[成功]',
    '✗': '[错误]',
    '⚠️': '[警告]',
    '💡': '[提示]',
    '🎉': '[庆祝]',
    '📋': '[列表]',
    '🆕': '[新]',
    '🔧': '[工具]',
    '📁': '[文件夹]',
    '🎯': '[目标]',
    '🏗️': '[架构]',
    '🔮': '[未来]',
}

def fix_unicode_in_file(file_path):
    """修复单个文件中的Unicode字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换Unicode字符
        for unicode_char, replacement in UNICODE_REPLACEMENTS.items():
            content = content.replace(unicode_char, replacement)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    # 需要处理的文件模式
    patterns = [
        'src/**/*.py',
        'test/**/*.py',
        'scripts/*.py',
        'hardware/*.py',
    ]
    
    fixed_files = []
    
    for pattern in patterns:
        for file_path in project_root.glob(pattern):
            if file_path.is_file() and file_path.name != 'fix_unicode.py':
                if fix_unicode_in_file(file_path):
                    fixed_files.append(str(file_path.relative_to(project_root)))
    
    print(f"\n修复完成！共处理 {len(fixed_files)} 个文件:")
    for file_path in fixed_files:
        print(f"  - {file_path}")

if __name__ == "__main__":
    main()
